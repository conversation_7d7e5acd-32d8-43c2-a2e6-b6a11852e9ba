<!--pages/maintenance/checkin-step2.wxml-->
<view class="checkin-container">
  <!-- 步骤条 -->
  <view class="steps-container">
    <view class="steps">
      <view class="step completed">
        <view class="step-icon">✓</view>
        <text class="step-title">作业内容</text>
      </view>
      <view class="step-line completed"></view>
      <view class="step active">
        <view class="step-icon">2</view>
        <text class="step-title">人脸识别</text>
      </view>
      <view class="step-line"></view>
      <view class="step">
        <view class="step-icon">3</view>
        <text class="step-title">现场交底</text>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 提示信息 -->
    <view class="tip-section">
      <text class="tip-icon">🔐</text>
      <text class="tip-title">身份验证</text>
      <text class="tip-desc">请进行人脸识别验证身份，确保本人操作</text>
    </view>

    <!-- 人脸识别区域 -->
    <view class="face-recognition-section">
      <!-- 相机预览区域 -->
      <view class="camera-container" wx:if="{{!facePhoto && !recognitionResult}}">
        <camera 
          class="camera-preview"
          device-position="front"
          flash="off"
          binderror="onCameraError"
          bindstop="onCameraStop"
          bindready="onCameraReady"
        ></camera>
        
        <!-- 人脸框指引 -->
        <view class="face-guide">
          <view class="face-frame">
            <view class="frame-corner tl"></view>
            <view class="frame-corner tr"></view>
            <view class="frame-corner bl"></view>
            <view class="frame-corner br"></view>
          </view>
          <text class="guide-text">请将面部对准框内</text>
        </view>
        
        <!-- 拍照按钮 -->
        <view class="capture-controls">
          <button class="capture-btn" bindtap="takeFacePhoto">
            <view class="capture-icon"></view>
          </button>
        </view>
      </view>

      <!-- 照片预览区域 -->
      <view class="photo-preview" wx:if="{{facePhoto && !recognitionResult}}">
        <image class="preview-image" src="{{facePhoto}}" mode="aspectFit"></image>
        <view class="preview-actions">
          <button class="action-btn retry-btn" bindtap="retakePhoto">重新拍摄</button>
          <button class="action-btn confirm-btn" bindtap="startRecognition">确认识别</button>
        </view>
      </view>

      <!-- 识别结果区域 -->
      <view class="recognition-result" wx:if="{{recognitionResult}}">
        <view class="result-header">
          <view class="result-status {{recognitionResult.success ? 'success' : 'failed'}}">
            <text class="status-icon">{{recognitionResult.success ? '✓' : '✗'}}</text>
            <text class="status-text">{{recognitionResult.success ? '识别成功' : '识别失败'}}</text>
          </view>
        </view>
        
        <view class="result-content" wx:if="{{recognitionResult.success}}">
          <view class="user-info">
            <image class="user-avatar" src="{{facePhoto}}" mode="aspectFill"></image>
            <view class="user-details">
              <text class="user-name">{{recognitionResult.userName}}</text>
              <text class="user-id">工号：{{recognitionResult.userId}}</text>
              <text class="confidence">置信度：{{recognitionResult.confidence}}%</text>
            </view>
          </view>
          
          <view class="verification-info">
            <text class="verify-time">验证时间：{{recognitionResult.verifyTime}}</text>
            <text class="verify-location">验证地点：{{stepData.checkinLocation.address}}</text>
          </view>
        </view>
        
        <view class="result-content" wx:else>
          <view class="error-info">
            <text class="error-icon">⚠️</text>
            <text class="error-message">{{recognitionResult.message}}</text>
            <text class="error-suggestion">请确保光线充足，面部清晰可见</text>
          </view>
          <button class="retry-recognition-btn" bindtap="retakePhoto">重新识别</button>
        </view>
      </view>

      <!-- 识别中状态 -->
      <view class="recognizing-overlay" wx:if="{{isRecognizing}}">
        <view class="recognizing-content">
          <view class="recognizing-spinner"></view>
          <text class="recognizing-text">正在进行人脸识别...</text>
          <text class="recognizing-tip">请保持面部稳定</text>
        </view>
      </view>
    </view>

    <!-- 安全提示 -->
    <view class="safety-tips">
      <view class="tips-header">
        <text class="tips-icon">💡</text>
        <text class="tips-title">识别提示</text>
      </view>
      <view class="tips-list">
        <text class="tip-item">• 请确保光线充足，避免逆光拍摄</text>
        <text class="tip-item">• 摘下帽子、眼镜等遮挡物</text>
        <text class="tip-item">• 保持面部正对摄像头</text>
        <text class="tip-item">• 表情自然，不要闭眼或张嘴</text>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="back-btn" bindtap="prevStep">
      <text class="btn-arrow">←</text>
      <text class="btn-text">上一步</text>
    </button>
    <button 
      class="next-btn {{canProceed ? '' : 'disabled'}}"
      bindtap="nextStep"
      disabled="{{!canProceed}}"
    >
      <text class="btn-text">下一步</text>
      <text class="btn-arrow">→</text>
    </button>
  </view>
</view>
