// pages/maintenance/checkout.js
const app = getApp()
const { maintenanceAPI, photoAPI } = require('../../utils/api')
const { formatTime, formatDuration, chooseImage, previewImage, showError, showSuccess } = require('../../utils/util')

Page({
  data: {
    recordId: '',
    recordInfo: {},
    workDuration: '',
    
    // 照片数据
    completionPhotos: [],
    toolsPhotos: [],
    
    // 表单数据
    completionStatus: 'completed', // completed, partial, failed
    workSummary: '',
    exceptionTypes: [],
    exceptionDescription: '',
    
    // 状态
    canCheckout: false,
    isSubmitting: false
  },

  onLoad(options) {
    console.log('离站打卡页面加载', options)
    
    const { recordId } = options
    
    if (!recordId) {
      showError('运维记录参数错误')
      wx.navigateBack()
      return
    }
    
    this.setData({ recordId })
    this.loadRecordInfo()
  },

  // 加载运维记录信息
  loadRecordInfo() {
    maintenanceAPI.getMaintenanceRecordDetail(this.data.recordId)
      .then(res => {
        const record = res.data
        
        // 处理记录数据
        const recordInfo = {
          ...record,
          statusText: this.getStatusText(record.status),
          workTypeText: this.getWorkTypeText(record.workType),
          safetyFactorsText: this.getSafetyFactorsText(record.safetyFactors),
          checkinTime: formatTime(record.checkinTime, 'MM-DD HH:mm')
        }
        
        // 计算作业时长
        const workDuration = this.calculateWorkDuration(record.checkinTime)
        
        this.setData({
          recordInfo,
          workDuration
        })
        
        this.checkCanCheckout()
      })
      .catch(error => {
        console.error('加载运维记录失败:', error)
        showError('加载运维记录失败')
        wx.navigateBack()
      })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      1: '进行中',
      2: '已完成',
      3: '异常'
    }
    return statusMap[status] || '未知'
  },

  // 获取作业类型文本
  getWorkTypeText(workType) {
    const typeMap = {
      'repair': '配套维修',
      'tower': '杆塔维修'
    }
    return typeMap[workType] || workType
  },

  // 获取安全要素文本
  getSafetyFactorsText(safetyFactors) {
    if (!safetyFactors) return ''
    
    const factors = safetyFactors.split(',')
    const factorTexts = factors.map(factor => {
      switch (factor) {
        case 'electric': return '涉电'
        case 'height': return '登高'
        default: return factor
      }
    })
    return factorTexts.join('、')
  },

  // 计算作业时长
  calculateWorkDuration(checkinTime) {
    if (!checkinTime) return '0分钟'
    
    const start = new Date(checkinTime)
    const now = new Date()
    const duration = Math.floor((now - start) / (1000 * 60)) // 分钟
    
    return formatDuration(duration)
  },

  // 拍照
  takePhoto(e) {
    const type = e.currentTarget.dataset.type
    
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
        
        chooseImage(1, ['compressed'], sourceType)
          .then(tempFilePaths => {
            this.uploadPhoto(tempFilePaths[0], type)
          })
          .catch(error => {
            console.error('选择图片失败:', error)
          })
      }
    })
  },

  // 上传照片
  uploadPhoto(filePath, type) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })
    
    const businessType = 'checkout'
    const businessId = this.data.recordId
    
    photoAPI.uploadPhoto(filePath, businessType, businessId)
      .then(res => {
        wx.hideLoading()
        
        const photoUrl = res.data.photoUrl
        
        if (type === 'completion') {
          const completionPhotos = [...this.data.completionPhotos, photoUrl]
          this.setData({ completionPhotos })
        } else if (type === 'tools') {
          const toolsPhotos = [...this.data.toolsPhotos, photoUrl]
          this.setData({ toolsPhotos })
        }
        
        this.checkCanCheckout()
        showSuccess('照片上传成功')
      })
      .catch(error => {
        wx.hideLoading()
        console.error('照片上传失败:', error)
        showError('照片上传失败，请重试')
      })
  },

  // 预览照片
  previewPhoto(e) {
    const urls = e.currentTarget.dataset.urls
    const current = e.currentTarget.dataset.current
    
    previewImage(urls, current)
  },

  // 删除照片
  deletePhoto(e) {
    const type = e.currentTarget.dataset.type
    const index = e.currentTarget.dataset.index
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？',
      success: (res) => {
        if (res.confirm) {
          if (type === 'completion') {
            const completionPhotos = [...this.data.completionPhotos]
            completionPhotos.splice(index, 1)
            this.setData({ completionPhotos })
          } else if (type === 'tools') {
            const toolsPhotos = [...this.data.toolsPhotos]
            toolsPhotos.splice(index, 1)
            this.setData({ toolsPhotos })
          }
          
          this.checkCanCheckout()
        }
      }
    })
  },

  // 选择完成状态
  selectCompletionStatus(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ 
      completionStatus: status,
      exceptionTypes: status === 'completed' ? [] : this.data.exceptionTypes
    })
    this.checkCanCheckout()
  },

  // 作业总结输入
  onWorkSummaryInput(e) {
    this.setData({ workSummary: e.detail.value })
  },

  // 切换异常类型
  toggleExceptionType(e) {
    const type = e.currentTarget.dataset.type
    const { exceptionTypes } = this.data
    
    let newTypes = [...exceptionTypes]
    
    if (newTypes.includes(type)) {
      newTypes = newTypes.filter(t => t !== type)
    } else {
      newTypes.push(type)
    }
    
    this.setData({ exceptionTypes: newTypes })
  },

  // 异常描述输入
  onExceptionDescriptionInput(e) {
    this.setData({ exceptionDescription: e.detail.value })
  },

  // 检查是否可以离站
  checkCanCheckout() {
    const {
      completionPhotos,
      toolsPhotos,
      completionStatus,
      exceptionTypes,
      exceptionDescription
    } = this.data
    
    let canCheckout = completionPhotos.length > 0 && toolsPhotos.length > 0
    
    // 如果不是正常完成，需要填写异常信息
    if (completionStatus !== 'completed') {
      canCheckout = canCheckout && 
                   exceptionTypes.length > 0 && 
                   exceptionDescription.trim() !== ''
    }
    
    this.setData({ canCheckout })
  },

  // 提交离站
  submitCheckout() {
    if (!this.data.canCheckout || this.data.isSubmitting) {
      return
    }
    
    const {
      recordId,
      completionPhotos,
      toolsPhotos,
      completionStatus,
      workSummary,
      exceptionTypes,
      exceptionDescription
    } = this.data
    
    // 验证必填项
    if (completionPhotos.length === 0) {
      showError('请拍摄作业完成照片')
      return
    }
    
    if (toolsPhotos.length === 0) {
      showError('请拍摄工具整理照片')
      return
    }
    
    if (completionStatus !== 'completed') {
      if (exceptionTypes.length === 0) {
        showError('请选择异常类型')
        return
      }
      
      if (!exceptionDescription.trim()) {
        showError('请填写异常描述')
        return
      }
    }
    
    this.setData({ isSubmitting: true })
    
    // 构建提交数据
    const checkoutData = {
      recordId,
      checkoutTime: formatTime(new Date(), 'YYYY-MM-DD HH:mm:ss'),
      completionStatus,
      completionPhotos,
      toolsPhotos,
      workSummary: workSummary.trim(),
      exceptionTypes: exceptionTypes.join(','),
      exceptionDescription: exceptionDescription.trim()
    }
    
    // 调用API提交离站
    maintenanceAPI.checkoutMaintenance(checkoutData)
      .then(res => {
        showSuccess('离站打卡成功')
        
        // 延迟跳转到记录详情页面
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/maintenance/record-detail?recordId=${recordId}`
          })
        }, 1500)
      })
      .catch(error => {
        console.error('离站打卡失败:', error)
        showError(error.message || '离站打卡失败，请重试')
      })
      .finally(() => {
        this.setData({ isSubmitting: false })
      })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '离站打卡 - 运维宝',
      path: `/pages/maintenance/checkout?recordId=${this.data.recordId}`,
      imageUrl: '/images/share-checkout.png'
    }
  }
})
