// pages/site/new-site.js
const app = getApp()
const { siteAPI, photoAPI } = require('../../utils/api')
const { getCurrentLocation, chooseImage, previewImage, showError, showSuccess } = require('../../utils/util')

Page({
  data: {
    projectId: '',
    returnPage: '', // 返回页面标识
    
    // 表单数据
    siteName: '',
    siteCode: '',
    siteTypeIndex: -1,
    siteTypeList: [
      { value: 'room', name: '机房' },
      { value: 'tower', name: '杆塔' },
      { value: 'substation', name: '变电站' },
      { value: 'line', name: '线路' }
    ],
    siteAddress: '',
    longitude: '',
    latitude: '',
    sitePhotos: [],
    siteRemark: '',
    
    // 位置相关
    currentLocation: null,
    locationStatus: '', // loading, success, error
    
    // 状态
    canSubmit: false,
    isSubmitting: false
  },

  onLoad(options) {
    console.log('新增站点页面加载', options)
    
    const { projectId, returnPage } = options
    
    if (!projectId) {
      showError('项目参数错误')
      wx.navigateBack()
      return
    }
    
    this.setData({
      projectId,
      returnPage: returnPage || 'site-selection'
    })
  },

  // 站点名称输入
  onSiteNameInput(e) {
    this.setData({ siteName: e.detail.value })
    this.checkCanSubmit()
  },

  // 站点编码输入
  onSiteCodeInput(e) {
    this.setData({ siteCode: e.detail.value })
    this.checkCanSubmit()
  },

  // 站点类型选择
  onSiteTypeChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({ siteTypeIndex: index })
    this.checkCanSubmit()
  },

  // 站点地址输入
  onSiteAddressInput(e) {
    this.setData({ siteAddress: e.detail.value })
    this.checkCanSubmit()
  },

  // 经度输入
  onLongitudeInput(e) {
    this.setData({ longitude: e.detail.value })
    this.checkCanSubmit()
  },

  // 纬度输入
  onLatitudeInput(e) {
    this.setData({ latitude: e.detail.value })
    this.checkCanSubmit()
  },

  // 备注输入
  onSiteRemarkInput(e) {
    this.setData({ siteRemark: e.detail.value })
  },

  // 获取当前位置
  getCurrentLocation() {
    if (this.data.locationStatus === 'loading') return
    
    this.setData({ locationStatus: 'loading' })
    
    getCurrentLocation()
      .then(location => {
        this.setData({
          currentLocation: location,
          locationStatus: 'success',
          longitude: location.longitude.toString(),
          latitude: location.latitude.toString()
        })
        
        this.checkCanSubmit()
        showSuccess('定位成功')
      })
      .catch(error => {
        console.error('获取位置失败:', error)
        this.setData({ locationStatus: 'error' })
        
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '位置权限',
            content: '需要获取您的位置信息来设置站点坐标，请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      setTimeout(() => {
                        this.getCurrentLocation()
                      }, 1000)
                    }
                  }
                })
              }
            }
          })
        } else {
          showError('获取位置失败，请手动输入坐标')
        }
      })
  },

  // 拍照
  takePhoto() {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
        
        chooseImage(1, ['compressed'], sourceType)
          .then(tempFilePaths => {
            this.uploadPhoto(tempFilePaths[0])
          })
          .catch(error => {
            console.error('选择图片失败:', error)
          })
      }
    })
  },

  // 上传照片
  uploadPhoto(filePath) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })
    
    const businessType = 'site'
    const businessId = this.data.projectId
    
    photoAPI.uploadPhoto(filePath, businessType, businessId)
      .then(res => {
        wx.hideLoading()
        
        const photoUrl = res.data.photoUrl
        const sitePhotos = [...this.data.sitePhotos, photoUrl]
        
        this.setData({ sitePhotos })
        showSuccess('照片上传成功')
      })
      .catch(error => {
        wx.hideLoading()
        console.error('照片上传失败:', error)
        showError('照片上传失败，请重试')
      })
  },

  // 预览照片
  previewPhoto(e) {
    const urls = e.currentTarget.dataset.urls
    const current = e.currentTarget.dataset.current
    
    previewImage(urls, current)
  },

  // 删除照片
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？',
      success: (res) => {
        if (res.confirm) {
          const sitePhotos = [...this.data.sitePhotos]
          sitePhotos.splice(index, 1)
          this.setData({ sitePhotos })
        }
      }
    })
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const {
      siteName,
      siteCode,
      siteTypeIndex,
      siteAddress,
      longitude,
      latitude
    } = this.data
    
    const canSubmit = siteName.trim() !== '' &&
                     siteCode.trim() !== '' &&
                     siteTypeIndex !== -1 &&
                     siteAddress.trim() !== '' &&
                     longitude.trim() !== '' &&
                     latitude.trim() !== '' &&
                     this.isValidCoordinate(longitude, latitude)
    
    this.setData({ canSubmit })
  },

  // 验证坐标格式
  isValidCoordinate(longitude, latitude) {
    const lng = parseFloat(longitude)
    const lat = parseFloat(latitude)
    
    return !isNaN(lng) && !isNaN(lat) &&
           lng >= -180 && lng <= 180 &&
           lat >= -90 && lat <= 90
  },

  // 提交站点
  submitSite(e) {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return
    }
    
    const {
      projectId,
      siteName,
      siteCode,
      siteTypeIndex,
      siteTypeList,
      siteAddress,
      longitude,
      latitude,
      sitePhotos,
      siteRemark
    } = this.data
    
    // 验证必填项
    if (!siteName.trim()) {
      showError('请输入站点名称')
      return
    }
    
    if (!siteCode.trim()) {
      showError('请输入站点编码')
      return
    }
    
    if (siteTypeIndex === -1) {
      showError('请选择站点类型')
      return
    }
    
    if (!siteAddress.trim()) {
      showError('请输入站点地址')
      return
    }
    
    if (!longitude.trim() || !latitude.trim()) {
      showError('请输入经纬度坐标')
      return
    }
    
    if (!this.isValidCoordinate(longitude, latitude)) {
      showError('请输入有效的经纬度坐标')
      return
    }
    
    this.setData({ isSubmitting: true })
    
    // 构建提交数据
    const siteData = {
      projectId,
      siteName: siteName.trim(),
      siteCode: siteCode.trim(),
      siteType: siteTypeList[siteTypeIndex].value,
      siteAddress: siteAddress.trim(),
      longitude: parseFloat(longitude),
      latitude: parseFloat(latitude),
      sitePhotos,
      siteRemark: siteRemark.trim()
    }
    
    // 调用API创建站点
    siteAPI.createSite(siteData)
      .then(res => {
        showSuccess('站点创建成功')
        
        // 标记新站点已添加
        wx.setStorageSync('newSiteAdded', true)
        
        // 如果是从打卡流程来的，将新站点设置为选中状态
        if (this.data.returnPage === 'checkin') {
          const newSite = {
            id: res.data.siteId,
            siteName: siteData.siteName,
            siteCode: siteData.siteCode,
            siteAddress: siteData.siteAddress,
            siteType: siteData.siteType,
            longitude: siteData.longitude,
            latitude: siteData.latitude
          }
          wx.setStorageSync('selectedSiteForCheckin', newSite)
        }
        
        // 延迟返回
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      })
      .catch(error => {
        console.error('创建站点失败:', error)
        showError(error.message || '创建站点失败，请重试')
      })
      .finally(() => {
        this.setData({ isSubmitting: false })
      })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '新增站点 - 运维宝',
      path: `/pages/site/new-site?projectId=${this.data.projectId}`,
      imageUrl: '/images/share-new-site.png'
    }
  }
})
