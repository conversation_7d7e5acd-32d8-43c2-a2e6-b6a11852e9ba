<!--pages/maintenance/checkin-step3.wxml-->
<view class="checkin-container">
  <!-- 步骤条 -->
  <view class="steps-container">
    <view class="steps">
      <view class="step completed">
        <view class="step-icon">✓</view>
        <text class="step-title">作业内容</text>
      </view>
      <view class="step-line completed"></view>
      <view class="step completed">
        <view class="step-icon">✓</view>
        <text class="step-title">人脸识别</text>
      </view>
      <view class="step-line completed"></view>
      <view class="step active">
        <view class="step-icon">3</view>
        <text class="step-title">现场交底</text>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 作业信息确认 -->
    <view class="info-confirm-section">
      <view class="section-title">
        <text class="title-text">作业信息确认</text>
      </view>
      
      <view class="info-card">
        <view class="info-row">
          <text class="info-label">作业类型：</text>
          <text class="info-value">{{workTypeText}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">作业站点：</text>
          <text class="info-value">{{stepData.siteName}}</text>
        </view>
        <view class="info-row" wx:if="{{stepData.safetyFactors}}">
          <text class="info-label">安全要素：</text>
          <text class="info-value">{{safetyFactorsText}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">作业人员：</text>
          <text class="info-value">{{stepData.faceRecognition.userName}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">打卡时间：</text>
          <text class="info-value">{{checkinTime}}</text>
        </view>
      </view>
    </view>

    <!-- 现场照片拍摄 -->
    <view class="photo-section">
      <view class="section-title">
        <text class="title-text">现场照片</text>
        <text class="required-mark">*</text>
      </view>
      
      <!-- 作业现场照片 -->
      <view class="photo-group">
        <view class="photo-header">
          <text class="photo-label">作业现场</text>
          <text class="photo-desc">请拍摄作业现场全景照片</text>
        </view>
        <view class="photo-grid">
          <view class="photo-item" wx:for="{{scenePhotos}}" wx:key="index">
            <image 
              class="photo-image" 
              src="{{item}}" 
              mode="aspectFill"
              bindtap="previewPhoto"
              data-urls="{{scenePhotos}}"
              data-current="{{index}}"
            />
            <view class="photo-delete" bindtap="deletePhoto" data-type="scene" data-index="{{index}}">
              <text class="delete-icon">×</text>
            </view>
          </view>
          <view 
            class="photo-add-btn" 
            wx:if="{{scenePhotos.length < 3}}"
            bindtap="takePhoto" 
            data-type="scene"
          >
            <text class="add-icon">📷</text>
            <text class="add-text">拍摄现场</text>
          </view>
        </view>
      </view>

      <!-- 防护用品照片 -->
      <view class="photo-group">
        <view class="photo-header">
          <text class="photo-label">防护用品</text>
          <text class="photo-desc">请拍摄个人防护用品佩戴情况</text>
        </view>
        <view class="photo-grid">
          <view class="photo-item" wx:for="{{safetyGearPhotos}}" wx:key="index">
            <image 
              class="photo-image" 
              src="{{item}}" 
              mode="aspectFill"
              bindtap="previewPhoto"
              data-urls="{{safetyGearPhotos}}"
              data-current="{{index}}"
            />
            <view class="photo-delete" bindtap="deletePhoto" data-type="gear" data-index="{{index}}">
              <text class="delete-icon">×</text>
            </view>
          </view>
          <view 
            class="photo-add-btn" 
            wx:if="{{safetyGearPhotos.length < 2}}"
            bindtap="takePhoto" 
            data-type="gear"
          >
            <text class="add-icon">📷</text>
            <text class="add-text">拍摄防护</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 作业备注 -->
    <view class="remark-section">
      <view class="section-title">
        <text class="title-text">作业备注</text>
      </view>
      <textarea 
        class="remark-textarea" 
        placeholder="请输入作业备注信息（选填，上限200字）"
        value="{{workRemark}}"
        bindinput="onRemarkInput"
        maxlength="200"
        auto-height
      />
      <text class="input-counter">{{workRemark.length}}/200</text>
    </view>

    <!-- AI检测结果 -->
    <view class="ai-detection-section" wx:if="{{aiDetectionResults.length > 0}}">
      <view class="section-title">
        <text class="title-text">AI安全检测</text>
      </view>
      
      <view class="detection-results">
        <view class="detection-item" wx:for="{{aiDetectionResults}}" wx:key="id">
          <view class="detection-header">
            <text class="detection-type">{{item.typeName}}</text>
            <view class="detection-status {{item.status === 'pass' ? 'pass' : item.status === 'warning' ? 'warning' : 'fail'}}">
              <text class="status-icon">{{item.status === 'pass' ? '✓' : item.status === 'warning' ? '⚠' : '✗'}}</text>
              <text class="status-text">{{item.statusText}}</text>
            </view>
          </view>
          <text class="detection-desc" wx:if="{{item.description}}">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="back-btn" bindtap="prevStep">
      <text class="btn-arrow">←</text>
      <text class="btn-text">上一步</text>
    </button>
    <button 
      class="complete-btn {{canComplete ? '' : 'disabled'}}"
      bindtap="completeCheckin"
      disabled="{{!canComplete || isSubmitting}}"
    >
      <text class="btn-text">{{isSubmitting ? '提交中...' : '完成打卡'}}</text>
    </button>
  </view>

  <!-- 提交遮罩 -->
  <view class="submit-overlay" wx:if="{{isSubmitting}}">
    <view class="submit-content">
      <view class="submit-spinner"></view>
      <text class="submit-text">正在提交打卡信息...</text>
      <text class="submit-tip">请稍候，正在进行AI安全检测</text>
    </view>
  </view>
</view>
