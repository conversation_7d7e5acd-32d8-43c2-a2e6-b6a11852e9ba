/* pages/maintenance/checkin-step1.wxss */
.checkin-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 步骤条 */
.steps-container {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  background: #e0e0e0;
  color: #999;
  transition: all 0.3s ease;
}

.step.active .step-icon {
  background: #1976d2;
  color: #fff;
  transform: scale(1.1);
}

.step.completed .step-icon {
  background: #4caf50;
  color: #fff;
}

.step-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.step.active .step-title {
  color: #1976d2;
  font-weight: 600;
}

.step.completed .step-title {
  color: #4caf50;
  font-weight: 600;
}

.step-line {
  position: absolute;
  top: 32rpx;
  left: 50%;
  width: 100%;
  height: 2rpx;
  background: #e0e0e0;
  z-index: -1;
}

.step:last-child .step-line {
  display: none;
}

.step.completed .step-line {
  background: #4caf50;
}

/* 表单容器 */
.form-container {
  padding: 32rpx;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.required-mark {
  color: #f44336;
  font-size: 32rpx;
  margin-left: 8rpx;
}

/* 单选组 */
.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  flex: 1;
  transition: all 0.3s ease;
}

.radio-item.selected {
  border-color: #1976d2;
  background: #e3f2fd;
}

.radio-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d0d0d0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-item.selected .radio-icon {
  border-color: #1976d2;
  background: #1976d2;
}

.radio-dot {
  color: #fff;
  font-size: 24rpx;
}

.radio-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 站点选择 */
.site-selector {
  margin-bottom: 24rpx;
}

.site-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.site-input:active {
  border-color: #1976d2;
  background: #fafafa;
}

.site-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999;
}

.site-info {
  margin-top: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #1976d2;
}

.site-code,
.site-address,
.site-type {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.5;
}

.site-type {
  margin-bottom: 0;
}

.new-site-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.new-site-btn:active {
  border-color: #1976d2;
  background: #f0f8ff;
}

.btn-icon {
  font-size: 32rpx;
  color: #1976d2;
  font-weight: bold;
}

.btn-text {
  font-size: 28rpx;
  color: #1976d2;
  font-weight: 500;
}

/* 复选组 */
.checkbox-group {
  display: flex;
  gap: 32rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  flex: 1;
  transition: all 0.3s ease;
}

.checkbox-item.checked {
  border-color: #4caf50;
  background: #e8f5e8;
}

.checkbox-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d0d0d0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox-item.checked .checkbox-icon {
  border-color: #4caf50;
  background: #4caf50;
}

.check-mark {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.checkbox-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 定位区域 */
.location-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.location-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: 2rpx solid #e0e0e0;
  background: #fff;
  color: #333;
  transition: all 0.3s ease;
}

.location-btn:not([disabled]):active {
  border-color: #1976d2;
  background: #f0f8ff;
}

.location-btn.success {
  border-color: #4caf50;
  background: #e8f5e8;
  color: #4caf50;
}

.location-btn.loading {
  border-color: #ff9800;
  background: #fff3e0;
  color: #ff9800;
}

.location-btn[disabled] {
  opacity: 0.6;
}

.location-info {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #1976d2;
}

.location-address {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.location-coords {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.distance-check {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.distance-check.valid {
  background: #e8f5e8;
  color: #4caf50;
}

.distance-check.invalid {
  background: #ffebee;
  color: #f44336;
}

.distance-icon {
  font-size: 20rpx;
}

.location-tip {
  padding: 16rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
  border-left: 4rpx solid #1976d2;
}

.tip-text {
  font-size: 24rpx;
  color: #1976d2;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.next-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.next-btn:not(.disabled) {
  background: #1976d2;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.next-btn:not(.disabled):active {
  background: #1565c0;
  transform: translateY(2rpx);
}

.next-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-arrow {
  font-size: 28rpx;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .radio-group,
  .checkbox-group {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .radio-item,
  .checkbox-item {
    flex: none;
  }
}
