/* pages/maintenance/checkout.wxss */
.checkout-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 运维记录信息 */
.record-info-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.required-mark {
  color: #f44336;
  font-size: 32rpx;
  margin-left: 8rpx;
}

.record-card {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #1976d2;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.record-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.record-status.ongoing {
  background: #fff3e0;
  color: #ff9800;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  align-items: flex-start;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

/* 照片区域 */
.photo-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.photo-group {
  margin-bottom: 40rpx;
}

.photo-group:last-child {
  margin-bottom: 0;
}

.photo-header {
  margin-bottom: 20rpx;
}

.photo-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.photo-desc {
  font-size: 24rpx;
  color: #666;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.photo-add-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.photo-add-btn:active {
  background: #f0f0f0;
  border-color: #1976d2;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.add-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 作业总结 */
.summary-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.form-group {
  margin-bottom: 32rpx;
  position: relative;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.completion-status {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.status-option.selected {
  border-color: #1976d2;
  background: #e3f2fd;
}

.option-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d0d0d0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-option.selected .option-icon {
  border-color: #1976d2;
  background: #1976d2;
}

.icon-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  color: #333;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #1976d2;
  outline: none;
}

.form-textarea::placeholder {
  color: #999;
}

.input-counter {
  position: absolute;
  right: 16rpx;
  bottom: -32rpx;
  font-size: 24rpx;
  color: #999;
}

/* 异常情况 */
.exception-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border-left: 4rpx solid #ff9800;
}

.exception-types {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.exception-tag {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.exception-tag.selected {
  border-color: #ff9800;
  background: #fff3e0;
}

.tag-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.exception-tag.selected .tag-text {
  color: #ff9800;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.checkout-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkout-btn:not(.disabled) {
  background: #4caf50;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

.checkout-btn:not(.disabled):active {
  background: #45a049;
  transform: translateY(2rpx);
}

.checkout-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-text {
  font-size: 32rpx;
}

/* 提交遮罩 */
.submit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.submit-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 32rpx;
}

.submit-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4caf50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submit-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.submit-tip {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .photo-item,
  .photo-add-btn {
    width: 160rpx;
    height: 160rpx;
  }
  
  .add-icon {
    font-size: 40rpx;
  }
  
  .add-text {
    font-size: 22rpx;
  }
  
  .completion-status {
    gap: 12rpx;
  }
  
  .status-option {
    padding: 16rpx 20rpx;
  }
}
