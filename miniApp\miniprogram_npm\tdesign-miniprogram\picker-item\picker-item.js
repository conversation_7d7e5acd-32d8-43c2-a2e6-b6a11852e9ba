import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-picker-item`,ANIMATION_DURATION=1e3,INERTIA_TIME=300,INERTIA_DISTANCE=15,range=function(t,e,i){return Math.min(Math.max(t,e),i)},momentum=(t,e)=>{let i=t;return i=Math.abs(i/e)/.005*(i<0?-1:1),i};let PickerItem=class extends SuperComponent{constructor(){super(...arguments),this.relations={"../picker/picker":{type:"parent",linked(t){if("keys"in t.data){const{keys:e}=t.data;if(null===e||JSON.stringify(this.data.pickerKeys)===JSON.stringify(e))return;this.setData({pickerKeys:e})}}}},this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`],this.properties=props,this.observers={"options, pickerKeys"(){this.update()}},this.data={prefix:prefix,classPrefix:name,offset:0,duration:0,value:"",curIndex:0,columnIndex:0,pickerKeys:{value:"value",label:"label"},formatOptions:props.options.value},this.lifetimes={created(){this.StartY=0,this.StartOffset=0,this.startTime=0}},this.methods={onTouchStart(t){this.StartY=t.touches[0].clientY,this.StartOffset=this.data.offset,this.startTime=Date.now(),this.setData({duration:0})},onTouchMove(t){const{StartY:e,StartOffset:i}=this,{pickItemHeight:s}=this.data,o=t.touches[0].clientY-e,n=range(i+o,-this.getCount()*s,0);this.setData({offset:n})},onTouchEnd(t){const{offset:e,pickerKeys:i,columnIndex:s,pickItemHeight:o,formatOptions:n}=this.data,{startTime:a}=this;if(e===this.StartOffset)return;let r=0;const l=t.changedTouches[0].clientY-this.StartY,d=Date.now()-a;d<300&&Math.abs(l)>15&&(r=momentum(l,d));const c=range(e+r,-this.getCount()*o,0),u=range(Math.round(-c/o),0,this.getCount()-1);this.setData({offset:-u*o,duration:1e3,curIndex:u}),u!==this._selectedIndex&&(this._selectedIndex=u,wx.nextTick((()=>{var t,e,o;this._selectedIndex=u,this._selectedValue=null===(t=n[u])||void 0===t?void 0:t[null==i?void 0:i.value],this._selectedLabel=null===(e=n[u])||void 0===e?void 0:e[null==i?void 0:i.label],null===(o=this.$parent)||void 0===o||o.triggerColumnChange({index:u,column:s})})))},formatOption:(t,e,i)=>"function"!=typeof i?t:t.map((t=>i(t,e))),update(){var t,e,i,s;const{options:o,value:n,pickerKeys:a,pickItemHeight:r,format:l,columnIndex:d}=this.data,c=this.formatOption(o,d,l),u=c.findIndex((t=>t[null==a?void 0:a.value]===n)),h=u>0?u:0;this._selectedIndex=h,this._selectedValue=null===(t=c[h])||void 0===t?void 0:t[null==a?void 0:a.value],this._selectedLabel=null===(e=c[h])||void 0===e?void 0:e[null==a?void 0:a.label],this.setData({formatOptions:c,offset:-h*r,curIndex:h}),this._selectedIndex=h,this._selectedValue=null===(i=o[h])||void 0===i?void 0:i[null==a?void 0:a.value],this._selectedLabel=null===(s=o[h])||void 0===s?void 0:s[null==a?void 0:a.label]},getCount(){var t,e;return null===(e=null===(t=this.data)||void 0===t?void 0:t.options)||void 0===e?void 0:e.length}}}};PickerItem=__decorate([wxComponent()],PickerItem);export default PickerItem;