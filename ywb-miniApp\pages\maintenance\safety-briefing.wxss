/* pages/maintenance/safety-briefing.wxss */
.briefing-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 提示信息 */
.briefing-tip {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #e3f2fd;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  border-left: 4rpx solid #1976d2;
}

.tip-icon {
  font-size: 32rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #1976d2;
  font-weight: 500;
}

/* 基本信息 */
.info-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 表单区域 */
.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.form-group {
  margin-bottom: 32rpx;
  position: relative;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-label.required::before {
  content: '*';
  color: #f44336;
  margin-right: 8rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  color: #333;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #1976d2;
  outline: none;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

.form-textarea {
  min-height: 120rpx;
  line-height: 1.6;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  font-size: 28rpx;
  color: #333;
}

.form-picker.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.input-counter {
  position: absolute;
  right: 16rpx;
  bottom: -32rpx;
  font-size: 24rpx;
  color: #999;
}

/* 拍摄区域 */
.photo-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  margin-bottom: 32rpx;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 24rpx;
  color: #666;
}

.photo-group {
  margin-bottom: 40rpx;
}

.photo-group:last-child {
  margin-bottom: 0;
}

.photo-header {
  margin-bottom: 16rpx;
}

.photo-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.photo-label.required::before {
  content: '*';
  color: #f44336;
  margin-right: 8rpx;
}

.photo-desc {
  font-size: 24rpx;
  color: #666;
}

.photo-upload-area {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.photo-upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.photo-upload-btn:active {
  background: #f0f0f0;
  border-color: #1976d2;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.safety-gear-tip {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #fff3e0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9800;
}

.safety-gear-tip .tip-text {
  font-size: 24rpx;
  color: #e65100;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn:not(.disabled) {
  background: #1976d2;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.submit-btn:not(.disabled):active {
  background: #1565c0;
  transform: translateY(2rpx);
}

.submit-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-text {
  font-size: 32rpx;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .photo-item,
  .photo-upload-btn {
    width: 160rpx;
    height: 160rpx;
  }
  
  .upload-icon {
    font-size: 40rpx;
  }
  
  .upload-text {
    font-size: 22rpx;
  }
}
