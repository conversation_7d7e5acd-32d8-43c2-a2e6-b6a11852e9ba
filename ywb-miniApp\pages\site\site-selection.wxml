<!--pages/site/site-selection.wxml-->
<view class="site-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索站点名称或编码"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="performSearch"
        confirm-type="search"
      />
      <text class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</text>
    </view>
    <button class="search-btn" bindtap="performSearch">搜索</button>
  </view>

  <!-- 筛选条件 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-tags">
        <view 
          class="filter-tag {{filterType === '' ? 'active' : ''}}"
          bindtap="selectFilter"
          data-type=""
        >
          <text class="tag-text">全部</text>
        </view>
        <view 
          class="filter-tag {{filterType === 'room' ? 'active' : ''}}"
          bindtap="selectFilter"
          data-type="room"
        >
          <text class="tag-text">机房</text>
        </view>
        <view 
          class="filter-tag {{filterType === 'tower' ? 'active' : ''}}"
          bindtap="selectFilter"
          data-type="tower"
        >
          <text class="tag-text">杆塔</text>
        </view>
        <view 
          class="filter-tag {{filterType === 'substation' ? 'active' : ''}}"
          bindtap="selectFilter"
          data-type="substation"
        >
          <text class="tag-text">变电站</text>
        </view>
        <view 
          class="filter-tag {{filterType === 'line' ? 'active' : ''}}"
          bindtap="selectFilter"
          data-type="line"
        >
          <text class="tag-text">线路</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 站点列表 -->
  <view class="site-list-section">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载站点...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{siteList.length === 0 && !loading}}">
      <text class="empty-icon">📍</text>
      <text class="empty-text">{{searchKeyword ? '未找到相关站点' : '暂无站点数据'}}</text>
      <button class="add-site-btn" bindtap="addNewSite">
        <text class="btn-text">新增站点</text>
      </button>
    </view>

    <!-- 站点列表 -->
    <scroll-view 
      class="site-scroll" 
      scroll-y="true" 
      wx:else
      bindscrolltolower="loadMoreSites"
      lower-threshold="100"
    >
      <view class="site-list">
        <view 
          class="site-item {{selectMode && selectedSiteId === item.id ? 'selected' : ''}}"
          wx:for="{{siteList}}" 
          wx:key="id"
          bindtap="{{selectMode ? 'selectSite' : 'viewSiteDetail'}}"
          data-site="{{item}}"
        >
          <view class="site-header">
            <view class="site-info">
              <text class="site-name">{{item.siteName}}</text>
              <text class="site-code">{{item.siteCode}}</text>
            </view>
            <view class="site-type-badge {{item.siteType}}">
              <text class="badge-text">{{item.siteTypeText}}</text>
            </view>
          </view>
          
          <view class="site-content">
            <view class="site-address">
              <text class="address-icon">📍</text>
              <text class="address-text">{{item.siteAddress}}</text>
            </view>
            
            <view class="site-meta">
              <text class="meta-item">经度：{{item.longitude}}</text>
              <text class="meta-item">纬度：{{item.latitude}}</text>
            </view>
            
            <view class="site-stats" wx:if="{{item.stats}}">
              <text class="stats-item">本月运维：{{item.stats.monthlyCount}}次</text>
              <text class="stats-item">最近运维：{{item.stats.lastMaintenanceDate || '无'}}</text>
            </view>
          </view>

          <!-- 选择模式的选中标识 -->
          <view class="select-indicator" wx:if="{{selectMode && selectedSiteId === item.id}}">
            <text class="indicator-icon">✓</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <view class="load-more-spinner" wx:if="{{loadingMore}}"></view>
        <text class="load-more-text">{{loadingMore ? '加载中...' : '上拉加载更多'}}</text>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && siteList.length > 0}}">
        <text class="no-more-text">没有更多站点了</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="add-btn" bindtap="addNewSite">
      <text class="btn-icon">+</text>
      <text class="btn-text">新增站点</text>
    </button>
    
    <button 
      class="confirm-btn {{selectMode && selectedSiteId ? '' : 'disabled'}}"
      wx:if="{{selectMode}}"
      bindtap="confirmSelection"
      disabled="{{!selectedSiteId}}"
    >
      <text class="btn-text">确认选择</text>
    </button>
  </view>
</view>
