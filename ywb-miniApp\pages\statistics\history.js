// pages/statistics/history.js
const app = getApp()
const { maintenanceAPI, statisticsAPI } = require('../../utils/api')
const { formatTime, formatDuration, showError } = require('../../utils/util')

Page({
  data: {
    // 筛选条件
    startDate: '',
    endDate: '',
    statusIndex: -1,
    statusList: [
      { value: '', name: '全部状态' },
      { value: 1, name: '进行中' },
      { value: 2, name: '已完成' },
      { value: 3, name: '异常' }
    ],
    
    // 搜索结果
    searchSummary: null,
    recordList: [],
    hasSearched: false,
    
    // 分页
    loading: false,
    loadingMore: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 20
  },

  onLoad() {
    console.log('历史运维页面加载')
    
    // 设置默认日期范围（最近30天）
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)
    
    this.setData({
      startDate: formatTime(startDate, 'YYYY-MM-DD'),
      endDate: formatTime(endDate, 'YYYY-MM-DD')
    })
    
    // 自动搜索
    this.searchRecords()
  },

  onPullDownRefresh() {
    this.refreshRecords().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 开始日期选择
  onStartDateChange(e) {
    this.setData({ startDate: e.detail.value })
  },

  // 结束日期选择
  onEndDateChange(e) {
    this.setData({ endDate: e.detail.value })
  },

  // 状态选择
  onStatusChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({ statusIndex: index })
  },

  // 搜索记录
  searchRecords() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      recordList: [],
      hasSearched: true
    })
    
    this.loadRecords()
    this.loadSearchSummary()
  },

  // 刷新记录
  refreshRecords() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      recordList: []
    })
    
    return Promise.all([
      this.loadRecords(),
      this.loadSearchSummary()
    ])
  },

  // 加载记录
  loadRecords() {
    if (this.data.loading && this.data.currentPage > 1) return Promise.resolve()
    
    this.setData({ 
      loading: this.data.currentPage === 1,
      loadingMore: this.data.currentPage > 1
    })
    
    const {
      startDate,
      endDate,
      statusIndex,
      statusList,
      currentPage,
      pageSize
    } = this.data
    
    // 构建查询参数
    const params = {
      userId: app.globalData.userInfo.id,
      startDate,
      endDate,
      status: statusIndex >= 0 ? statusList[statusIndex].value : '',
      page: currentPage,
      pageSize,
      orderBy: 'workDate',
      orderType: 'desc'
    }
    
    return maintenanceAPI.getMaintenanceRecordList(params)
      .then(res => {
        const { list, hasMore } = res.data
        
        // 处理记录数据
        const processedList = list.map(record => ({
          ...record,
          statusText: this.getStatusText(record.status),
          workTypeText: this.getWorkTypeText(record.workType),
          safetyFactorsText: this.getSafetyFactorsText(record.safetyFactors),
          workDate: formatTime(record.workDate, 'MM-DD'),
          checkinTime: record.checkinTime ? formatTime(record.checkinTime, 'HH:mm') : '',
          workDurationText: record.workDuration ? formatDuration(record.workDuration) : null
        }))
        
        if (currentPage === 1) {
          this.setData({
            recordList: processedList,
            hasMore
          })
        } else {
          this.setData({
            recordList: [...this.data.recordList, ...processedList],
            hasMore
          })
        }
        
        this.setData({ currentPage: currentPage + 1 })
      })
      .catch(error => {
        console.error('加载记录失败:', error)
        showError('加载记录失败')
      })
      .finally(() => {
        this.setData({
          loading: false,
          loadingMore: false
        })
      })
  },

  // 加载搜索统计
  loadSearchSummary() {
    const {
      startDate,
      endDate,
      statusIndex,
      statusList
    } = this.data
    
    const params = {
      userId: app.globalData.userInfo.id,
      startDate,
      endDate,
      status: statusIndex >= 0 ? statusList[statusIndex].value : ''
    }
    
    return statisticsAPI.getRecordSummary(params)
      .then(res => {
        const summary = res.data
        
        // 处理总时长显示
        const totalDuration = summary.totalDuration ? 
          formatDuration(summary.totalDuration) : '0分钟'
        
        this.setData({
          searchSummary: {
            ...summary,
            totalDuration
          }
        })
      })
      .catch(error => {
        console.error('加载搜索统计失败:', error)
        // 使用默认值
        this.setData({
          searchSummary: {
            totalCount: 0,
            completedCount: 0,
            totalDuration: '0分钟'
          }
        })
      })
  },

  // 加载更多记录
  loadMoreRecords() {
    if (!this.data.hasMore || this.data.loadingMore) return
    this.loadRecords()
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      1: '进行中',
      2: '已完成',
      3: '异常'
    }
    return statusMap[status] || '未知'
  },

  // 获取作业类型文本
  getWorkTypeText(workType) {
    const typeMap = {
      'repair': '配套维修',
      'tower': '杆塔维修'
    }
    return typeMap[workType] || workType
  },

  // 获取安全要素文本
  getSafetyFactorsText(safetyFactors) {
    if (!safetyFactors) return ''
    
    const factors = safetyFactors.split(',')
    const factorTexts = factors.map(factor => {
      switch (factor) {
        case 'electric': return '涉电'
        case 'height': return '登高'
        default: return factor
      }
    })
    return factorTexts.join('、')
  },

  // 查看记录详情
  viewRecordDetail(e) {
    const record = e.currentTarget.dataset.record
    
    wx.navigateTo({
      url: `/pages/maintenance/record-detail?recordId=${record.id}`
    })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '历史运维记录 - 运维宝',
      path: '/pages/statistics/history',
      imageUrl: '/images/share-history.png'
    }
  }
})
