<view class="pullDownList t-class {{ childBoxHeight > 0 ? 'actived' : '' }}">
  <view class="switchBox" aria-role="button" aria-expanded="{{!!childBoxHeight}}" catch:tap="switchHandle">
    <view class="name">{{ name }}</view>
    <t-icon name="{{icon}}" size="48rpx" color="#A6A6A6" t-class="icon" aria-hidden />
  </view>
  <view class="childBox" style="height: {{ childBoxHeight }}rpx" aria-hidden="{{childBoxHeight ? '' : true}}">
    <view class="child" wx:for="{{childArr}}" wx:key="name" data-item="{{item}}" aria-role="button" bind:tap="tapChild">
      {{ item.name }} {{ item.label }}
      <t-icon name="chevron-right" color="var(--td-text-color-placeholder)" aria-hidden />
    </view>
  </view>
</view>
