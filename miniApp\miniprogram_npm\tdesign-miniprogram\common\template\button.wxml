<template name="button"><t-button t-id="{{tId || ''}}" style="{{style || ''}}" block="{{block || false}}" class="{{rootClass || ''}}" t-class="{{tClass}}" disabled="{{disabled || false}}" data-type="{{type}}" data-extra="{{extra}}" custom-dataset="{{customDataset}}" content="{{content || ''}}" icon="{{icon || ''}}" loading="{{loading || false}}" loading-props="{{loadingProps || null }}" theme="{{theme || 'default'}}" ghost="{{ghost || false}}" shape="{{shape || 'rectangle'}}" size="{{size || 'medium'}}" variant="{{variant || 'base'}}" open-type="{{openType || ''}}" hover-class="{{hoverClass || ''}}" hover-stop-propagation="{{hoverStopPropagation || false}}" hover-start-time="{{hoverStartTime || 20}}" hover-stay-time="{{hoverStayTime || 70}}" lang="{{lang || 'en'}}" session-from="{{sessionFrom || ''}}" send-message-title="{{sendMessageTitle || ''}}" send-message-path="{{sendMessagePath || ''}}" send-message-img="{{sendMessageImg || ''}}" app-parameter="{{appParameter || ''}}" show-message-card="{{showMessageCard || false}}" bind:tap="onTplButtonTap" bind:getuserinfo="onTplButtonTap" bind:contact="onTplButtonTap" bind:getphonenumber="onTplButtonTap" bind:error="onTplButtonTap" bind:opensetting="onTplButtonTap" bind:launchapp="onTplButtonTap" bind:agreeprivacyauthorization="onTplButtonTap" aria-label="{{ariaLabel || ''}}"><slot wx:if="{{useDefaultSlot || false}}"/></t-button></template>