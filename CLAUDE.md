# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese operations and maintenance management platform (运维宝) project based on the RuoYi-Vue-Pro framework. It consists of:

- **Backend**: Spring Boot application with modular architecture
- **Frontend**: WeChat Mini Program for field operations
- **Database**: MySQL with MyBatis persistence layer
- **Core Features**: Operations project management, field service tracking, safety protocols, maintenance records

## Architecture

### Backend Structure
- **Framework**: RuoYi-Vue-Pro (芋道框架)
- **Java Version**: 1.8
- **Spring Boot**: 2.7.18
- **Build Tool**: Maven
- **Main Package**: `cn.iocoder.yudao`

### Key Modules
- `yudao-framework`: Core framework components
- `yudao-module-system`: System management (users, roles, permissions)
- `yudao-module-infra`: Infrastructure services
- `yudao-module-yunweibao`: Custom operations management module
- `yudao-server`: Main application entry point

### Frontend Structure
- **Mini Program**: WeChat-based field operations app
- **UI Framework**: TDesign components
- **Location**: Built-in with JTS for geographic calculations

## Common Development Commands

### Backend Development
```bash
# Build the project
mvn clean compile

# Run tests
mvn test

# Package application
mvn clean package

# Run application locally
mvn spring-boot:run

# Run with specific profile
java -jar yudao-server.jar --spring.profiles.active=development
```

### Database Operations
- Use MyBatis for database operations
- Entity classes follow pattern: `XxxDO.java`
- Mappers follow pattern: `XxxMapper.java`
- XML mappings in `src/main/resources/mapper/`

### Testing
- Unit tests in `src/test/java/`
- Use JUnit 5 framework
- Integration tests use Spring Boot Test

## Code Patterns and Conventions

### Backend Patterns
- **Controllers**: RESTful API endpoints with `@RestController`
- **Services**: Business logic in `@Service` classes
- **DTOs**: Data transfer objects for API communication
- **VOs**: View objects for frontend display
- **Enums**: Use `@InEnum` for validation
- **Validation**: Custom validators for mobile, telephone, etc.

### Database Patterns
- **Entities**: Use Lombok for getters/setters
- **Mappers**: MyBatis interfaces with XML mappings
- **Pagination**: Use `PageParam` and `PageResult` classes
- **Auditing**: Automatic creation/update timestamps

### WeChat Integration
- Use `weixin-java-miniapp` for mini-program integration
- Location services with JTS for geographic calculations
- WeChat login flow implementation

## Configuration

### Application Properties
- Main configuration: `src/main/resources/application.yml`
- Profile-specific: `application-{profile}.yml`
- Environment variables: `PROFILES_ACTIVE`

### Database Configuration
- MySQL database connection
- MyBatis configuration
- Redis for caching (if configured)

## Deployment

### Backend Deployment
```bash
# Using the provided deployment script
./script/shell/deploy.sh

# Manual deployment
java -server -Xms512m -Xmx512m -jar yudao-server.jar --spring.profiles.active=production
```

### Health Check
- Health endpoint: `/actuator/health`
- Default port: 48080

## Development Guidelines

### Adding New Features
1. Create controller in appropriate module
2. Implement service layer
3. Create mapper and database entities
4. Add DTOs for API communication
5. Write unit tests
6. Update API documentation

### Code Quality
- Follow existing code structure and patterns
- Use proper exception handling with `ServiceException`
- Implement proper validation using framework validators
- Use `CommonResult` for API responses
- Follow naming conventions for database tables and columns

### Security
- Use framework security components
- Implement proper authentication and authorization
- Follow data permission patterns if needed
- Validate all user inputs

## Module-Specific Notes

### yudao-module-yunweibao
- Custom module for operations management
- Includes field service tracking
- WeChat mini-program backend services
- Geographic location features
- Safety protocol management

### WeChat Mini Program
- Located in `miniApp/` directory
- Uses TDesign component library
- Field operations and maintenance features
- Location-based services