// components/photo-upload/photo-upload.js
const { chooseImage, previewImage } = require('../../utils/util')

Component({
  properties: {
    // 照片数组
    photos: {
      type: Array,
      value: []
    },
    // 最大数量
    maxCount: {
      type: Number,
      value: 3
    },
    // 添加按钮图标
    addIcon: {
      type: String,
      value: '📷'
    },
    // 添加按钮文本
    addText: {
      type: String,
      value: '添加照片'
    },
    // 提示文本
    tip: {
      type: String,
      value: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  data: {},

  methods: {
    // 添加照片
    addPhoto() {
      if (this.data.disabled) return
      
      const remainingCount = this.data.maxCount - this.data.photos.length
      if (remainingCount <= 0) return
      
      wx.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: (res) => {
          const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
          
          chooseImage(Math.min(remainingCount, 9), ['compressed'], sourceType)
            .then(tempFilePaths => {
              // 触发选择照片事件
              this.triggerEvent('photoadd', {
                tempFilePaths
              })
            })
            .catch(error => {
              console.error('选择图片失败:', error)
            })
        }
      })
    },

    // 预览照片
    previewPhoto(e) {
      const index = e.currentTarget.dataset.index
      previewImage(this.data.photos, index)
    },

    // 删除照片
    deletePhoto(e) {
      if (this.data.disabled) return
      
      const index = e.currentTarget.dataset.index
      
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这张照片吗？',
        success: (res) => {
          if (res.confirm) {
            // 触发删除照片事件
            this.triggerEvent('photodelete', {
              index
            })
          }
        }
      })
    }
  }
})
