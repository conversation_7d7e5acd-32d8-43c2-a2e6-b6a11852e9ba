// config/status-codes.js
// 接口状态码配置

// HTTP状态码
const HTTP_STATUS = {
  OK: 200,                    // 请求成功
  CREATED: 201,               // 创建成功
  NO_CONTENT: 204,            // 无内容
  BAD_REQUEST: 400,           // 请求错误
  UNAUTHORIZED: 401,          // 未授权
  FORBIDDEN: 403,             // 禁止访问
  NOT_FOUND: 404,             // 未找到
  METHOD_NOT_ALLOWED: 405,    // 方法不允许
  CONFLICT: 409,              // 冲突
  INTERNAL_SERVER_ERROR: 500, // 服务器内部错误
  BAD_GATEWAY: 502,           // 网关错误
  SERVICE_UNAVAILABLE: 503,   // 服务不可用
  GATEWAY_TIMEOUT: 504        // 网关超时
}

// 业务状态码
const BUSINESS_CODE = {
  SUCCESS: 0,                 // 成功
  
  // 通用错误 1000-1999
  PARAM_ERROR: 1001,          // 参数错误
  DATA_NOT_FOUND: 1002,       // 数据不存在
  DATA_ALREADY_EXISTS: 1003,  // 数据已存在
  OPERATION_FAILED: 1004,     // 操作失败
  PERMISSION_DENIED: 1005,    // 权限不足
  
  // 认证相关错误 2000-2999
  TOKEN_INVALID: 2001,        // token无效
  TOKEN_EXPIRED: 2002,        // token过期
  LOGIN_FAILED: 2003,         // 登录失败
  USER_NOT_EXISTS: 2004,      // 用户不存在
  PHONE_NOT_BOUND: 2005,      // 手机号未绑定
  WECHAT_AUTH_FAILED: 2006,   // 微信授权失败
  
  // 项目相关错误 3000-3999
  PROJECT_NOT_FOUND: 3001,    // 项目不存在
  PROJECT_ACCESS_DENIED: 3002, // 项目访问被拒绝
  PROJECT_STATUS_ERROR: 3003,  // 项目状态错误
  
  // 站点相关错误 4000-4999
  SITE_NOT_FOUND: 4001,       // 站点不存在
  SITE_TOO_FAR: 4002,         // 站点距离过远
  SITE_ACCESS_DENIED: 4003,   // 站点访问被拒绝
  
  // 运维相关错误 5000-5999
  ALREADY_CHECKED_IN: 5001,   // 已经打卡
  NOT_CHECKED_IN: 5002,       // 未打卡
  WORK_IN_PROGRESS: 5003,     // 作业进行中
  SAFETY_BRIEFING_REQUIRED: 5004, // 需要安全交底
  FACE_RECOGNITION_FAILED: 5005,  // 人脸识别失败
  LOCATION_VERIFICATION_FAILED: 5006, // 位置验证失败
  
  // 文件上传错误 6000-6999
  FILE_TOO_LARGE: 6001,       // 文件过大
  FILE_TYPE_NOT_ALLOWED: 6002, // 文件类型不允许
  UPLOAD_FAILED: 6003,        // 上传失败
  
  // AI分析错误 7000-7999
  AI_ANALYSIS_FAILED: 7001,   // AI分析失败
  FACE_NOT_DETECTED: 7002,    // 未检测到人脸
  SAFETY_GEAR_MISSING: 7003,  // 安全装备缺失
  ID_CARD_INVALID: 7004,      // 身份证无效
  
  // 系统错误 9000-9999
  SYSTEM_MAINTENANCE: 9001,   // 系统维护中
  SERVICE_UNAVAILABLE: 9002,  // 服务不可用
  RATE_LIMIT_EXCEEDED: 9003,  // 请求频率超限
  INTERNAL_ERROR: 9999        // 内部错误
}

// 状态码对应的错误消息
const ERROR_MESSAGES = {
  [BUSINESS_CODE.SUCCESS]: '操作成功',
  
  // 通用错误
  [BUSINESS_CODE.PARAM_ERROR]: '参数错误',
  [BUSINESS_CODE.DATA_NOT_FOUND]: '数据不存在',
  [BUSINESS_CODE.DATA_ALREADY_EXISTS]: '数据已存在',
  [BUSINESS_CODE.OPERATION_FAILED]: '操作失败',
  [BUSINESS_CODE.PERMISSION_DENIED]: '权限不足',
  
  // 认证相关错误
  [BUSINESS_CODE.TOKEN_INVALID]: 'Token无效，请重新登录',
  [BUSINESS_CODE.TOKEN_EXPIRED]: 'Token已过期，请重新登录',
  [BUSINESS_CODE.LOGIN_FAILED]: '登录失败',
  [BUSINESS_CODE.USER_NOT_EXISTS]: '用户不存在',
  [BUSINESS_CODE.PHONE_NOT_BOUND]: '请先绑定手机号',
  [BUSINESS_CODE.WECHAT_AUTH_FAILED]: '微信授权失败',
  
  // 项目相关错误
  [BUSINESS_CODE.PROJECT_NOT_FOUND]: '项目不存在',
  [BUSINESS_CODE.PROJECT_ACCESS_DENIED]: '无权访问该项目',
  [BUSINESS_CODE.PROJECT_STATUS_ERROR]: '项目状态错误',
  
  // 站点相关错误
  [BUSINESS_CODE.SITE_NOT_FOUND]: '站点不存在',
  [BUSINESS_CODE.SITE_TOO_FAR]: '距离站点过远，无法打卡',
  [BUSINESS_CODE.SITE_ACCESS_DENIED]: '无权访问该站点',
  
  // 运维相关错误
  [BUSINESS_CODE.ALREADY_CHECKED_IN]: '您已经打卡，请勿重复操作',
  [BUSINESS_CODE.NOT_CHECKED_IN]: '请先进行上站打卡',
  [BUSINESS_CODE.WORK_IN_PROGRESS]: '当前有作业进行中',
  [BUSINESS_CODE.SAFETY_BRIEFING_REQUIRED]: '请先完成安全交底',
  [BUSINESS_CODE.FACE_RECOGNITION_FAILED]: '人脸识别失败，请重试',
  [BUSINESS_CODE.LOCATION_VERIFICATION_FAILED]: '位置验证失败',
  
  // 文件上传错误
  [BUSINESS_CODE.FILE_TOO_LARGE]: '文件过大，请选择小于10MB的文件',
  [BUSINESS_CODE.FILE_TYPE_NOT_ALLOWED]: '文件类型不支持',
  [BUSINESS_CODE.UPLOAD_FAILED]: '文件上传失败，请重试',
  
  // AI分析错误
  [BUSINESS_CODE.AI_ANALYSIS_FAILED]: 'AI分析失败，请重试',
  [BUSINESS_CODE.FACE_NOT_DETECTED]: '未检测到人脸，请重新拍照',
  [BUSINESS_CODE.SAFETY_GEAR_MISSING]: '检测到安全装备缺失',
  [BUSINESS_CODE.ID_CARD_INVALID]: '身份证信息无效',
  
  // 系统错误
  [BUSINESS_CODE.SYSTEM_MAINTENANCE]: '系统维护中，请稍后再试',
  [BUSINESS_CODE.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  [BUSINESS_CODE.RATE_LIMIT_EXCEEDED]: '请求过于频繁，请稍后再试',
  [BUSINESS_CODE.INTERNAL_ERROR]: '系统内部错误'
}

// 获取错误消息
const getErrorMessage = (code) => {
  return ERROR_MESSAGES[code] || '未知错误'
}

// 判断是否为成功状态
const isSuccess = (code) => {
  return code === BUSINESS_CODE.SUCCESS
}

// 判断是否需要重新登录
const needReLogin = (code) => {
  return [
    BUSINESS_CODE.TOKEN_INVALID,
    BUSINESS_CODE.TOKEN_EXPIRED,
    BUSINESS_CODE.USER_NOT_EXISTS
  ].includes(code)
}

// 导出配置
module.exports = {
  HTTP_STATUS,
  BUSINESS_CODE,
  ERROR_MESSAGES,
  getErrorMessage,
  isSuccess,
  needReLogin
}
