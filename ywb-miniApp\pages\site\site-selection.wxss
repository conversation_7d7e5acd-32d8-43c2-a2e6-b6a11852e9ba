/* pages/site/site-selection.wxss */
.site-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 搜索区域 */
.search-section {
  background: #fff;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 0 20rpx;
  height: 72rpx;
  position: relative;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.clear-icon {
  font-size: 36rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

.search-btn {
  background: #1976d2;
  color: #fff;
  border-radius: 24rpx;
  padding: 0 32rpx;
  height: 72rpx;
  font-size: 28rpx;
  border: none;
  font-weight: 500;
}

.search-btn:active {
  background: #1565c0;
}

/* 筛选区域 */
.filter-section {
  background: #fff;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: inline-flex;
  padding: 0 32rpx;
  gap: 16rpx;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tag.active {
  background: #e3f2fd;
  border-color: #1976d2;
}

.tag-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.filter-tag.active .tag-text {
  color: #1976d2;
}

/* 站点列表区域 */
.site-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200rpx);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  gap: 24rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  gap: 24rpx;
}

.empty-icon {
  font-size: 120rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.add-site-btn {
  background: #1976d2;
  color: #fff;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  margin-top: 16rpx;
}

/* 站点滚动列表 */
.site-scroll {
  flex: 1;
  padding: 16rpx 0;
}

.site-list {
  padding: 0 32rpx;
}

.site-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.3s ease;
}

.site-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.12);
}

.site-item.selected {
  border: 2rpx solid #1976d2;
  background: #f8fcff;
}

.site-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.site-info {
  flex: 1;
}

.site-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.site-code {
  font-size: 24rpx;
  color: #666;
  font-family: 'Courier New', monospace;
}

.site-type-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  margin-left: 16rpx;
}

.site-type-badge.room {
  background: #e8f5e8;
  color: #4caf50;
}

.site-type-badge.tower {
  background: #fff3e0;
  color: #ff9800;
}

.site-type-badge.substation {
  background: #e3f2fd;
  color: #1976d2;
}

.site-type-badge.line {
  background: #f3e5f5;
  color: #9c27b0;
}

.site-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.site-address {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
}

.address-icon {
  font-size: 24rpx;
  color: #666;
  margin-top: 2rpx;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

.site-meta {
  display: flex;
  gap: 32rpx;
}

.meta-item {
  font-size: 24rpx;
  color: #999;
  font-family: 'Courier New', monospace;
}

.site-stats {
  display: flex;
  gap: 32rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stats-item {
  font-size: 24rpx;
  color: #666;
}

/* 选择指示器 */
.select-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
}

.load-more-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #f0f0f0;
  border-top: 2rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-text {
  font-size: 24rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 32rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
}

.add-btn,
.confirm-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.add-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.add-btn:active {
  background: #e0e0e0;
}

.confirm-btn {
  flex: 2;
}

.confirm-btn:not(.disabled) {
  background: #1976d2;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.confirm-btn:not(.disabled):active {
  background: #1565c0;
  transform: translateY(2rpx);
}

.confirm-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 28rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .site-meta,
  .site-stats {
    flex-direction: column;
    gap: 8rpx;
  }
  
  .site-header {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .site-type-badge {
    align-self: flex-start;
    margin-left: 0;
  }
}
