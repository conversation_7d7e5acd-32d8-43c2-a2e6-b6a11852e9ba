import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{isSameSecond,parseFormat,parseTimeData,TimeDataUnit}from"./utils";const{prefix:prefix}=config,name=`${prefix}-count-down`;let CountDown=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-count`,`${prefix}-class-split`],this.properties=props,this.observers={time(){this.reset()}},this.data={prefix:prefix,classPrefix:name,timeDataUnit:TimeDataUnit,timeData:parseTimeData(0),formattedTime:"0"},this.timeoutId=null,this.isInitialTime=!1,this.lifetimes={detached(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}},this.methods={start(){this.counting||(this.counting=!0,this.endTime=Date.now()+this.remain,this.doCount())},pause(){this.counting=!1,this.timeoutId&&clearTimeout(this.timeoutId)},reset(){this.pause(),this.remain=this.properties.time,this.updateTime(this.remain),this.properties.autoStart&&this.remain>0&&this.start(),this.isInitialTime=!0},getTime(){return Math.max(this.endTime-Date.now(),0)},updateTime(t){const{format:i}=this.properties;this.remain=t;const e=parseTimeData(t);this.triggerEvent("change",e);const{timeText:s}=parseFormat(t,i),o=i.split(":");this.setData({timeRange:o,timeData:e,formattedTime:s.replace(/:/g," : ")}),0===t&&(this.counting||this.isInitialTime)&&(this.pause(),this.triggerEvent("finish"),this.counting=!1)},doCount(){this.timeoutId=setTimeout((()=>{const t=this.getTime();this.properties.millisecond?this.updateTime(t):isSameSecond(t,this.remain)&&0!==t||this.updateTime(t),0!==t&&this.doCount()}),33)}}}};CountDown=__decorate([wxComponent()],CountDown);export default CountDown;