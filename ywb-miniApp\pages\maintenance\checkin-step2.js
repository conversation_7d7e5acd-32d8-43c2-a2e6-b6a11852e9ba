// pages/maintenance/checkin-step2.js
const app = getApp()
const { aiAPI, photoAPI } = require('../../utils/api')
const { formatTime, showError, showSuccess } = require('../../utils/util')

Page({
  data: {
    stepData: null,
    
    // 人脸识别相关
    facePhoto: '',
    recognitionResult: null,
    isRecognizing: false,
    
    // 状态
    canProceed: false,
    cameraReady: false
  },

  onLoad() {
    console.log('上站打卡步骤2加载')
    
    // 获取上一步传递的数据
    const stepData = wx.getStorageSync('checkinStepData')
    if (!stepData) {
      showError('数据异常，请重新开始')
      wx.navigateBack()
      return
    }
    
    this.setData({ stepData })
  },

  onShow() {
    // 检查相机权限
    this.checkCameraAuth()
  },

  onHide() {
    // 停止相机
    this.stopCamera()
  },

  onUnload() {
    // 停止相机
    this.stopCamera()
  },

  // 检查相机权限
  checkCameraAuth() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera'] === false) {
          // 用户拒绝了相机权限
          wx.showModal({
            title: '相机权限',
            content: '需要使用相机进行人脸识别，请在设置中开启相机权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting()
              }
            }
          })
        }
      }
    })
  },

  // 相机准备就绪
  onCameraReady() {
    console.log('相机准备就绪')
    this.setData({ cameraReady: true })
  },

  // 相机错误
  onCameraError(e) {
    console.error('相机错误:', e.detail)
    
    wx.showModal({
      title: '相机启动失败',
      content: '请检查相机权限或设备是否正常',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          // 重新加载页面
          wx.redirectTo({
            url: '/pages/maintenance/checkin-step2'
          })
        }
      }
    })
  },

  // 相机停止
  onCameraStop() {
    console.log('相机已停止')
    this.setData({ cameraReady: false })
  },

  // 停止相机
  stopCamera() {
    if (this.data.cameraReady) {
      const ctx = wx.createCameraContext()
      ctx.stopRecord()
    }
  },

  // 拍摄人脸照片
  takeFacePhoto() {
    if (!this.data.cameraReady) {
      showError('相机未准备就绪')
      return
    }
    
    const ctx = wx.createCameraContext()
    
    ctx.takePhoto({
      quality: 'high',
      success: (res) => {
        console.log('拍照成功:', res.tempImagePath)
        this.setData({
          facePhoto: res.tempImagePath
        })
      },
      fail: (error) => {
        console.error('拍照失败:', error)
        showError('拍照失败，请重试')
      }
    })
  },

  // 重新拍照
  retakePhoto() {
    this.setData({
      facePhoto: '',
      recognitionResult: null,
      canProceed: false
    })
  },

  // 开始人脸识别
  startRecognition() {
    if (!this.data.facePhoto) {
      showError('请先拍摄照片')
      return
    }
    
    this.setData({ isRecognizing: true })
    
    // 先上传照片
    this.uploadFacePhoto()
      .then(photoId => {
        // 调用人脸识别API
        return aiAPI.faceRecognition(photoId)
      })
      .then(res => {
        const result = res.data
        
        if (result.success) {
          // 识别成功
          this.setData({
            recognitionResult: {
              success: true,
              userName: result.userName,
              userId: result.userId,
              confidence: Math.round(result.confidence * 100),
              verifyTime: formatTime(new Date(), 'HH:mm:ss')
            },
            canProceed: true
          })
          
          showSuccess('人脸识别成功')
        } else {
          // 识别失败
          this.setData({
            recognitionResult: {
              success: false,
              message: result.message || '识别失败，请重试'
            },
            canProceed: false
          })
        }
      })
      .catch(error => {
        console.error('人脸识别失败:', error)
        
        this.setData({
          recognitionResult: {
            success: false,
            message: error.message || '识别服务异常，请重试'
          },
          canProceed: false
        })
      })
      .finally(() => {
        this.setData({ isRecognizing: false })
      })
  },

  // 上传人脸照片
  uploadFacePhoto() {
    return new Promise((resolve, reject) => {
      const businessType = 'face_recognition'
      const businessId = this.data.stepData.projectId
      
      photoAPI.uploadPhoto(this.data.facePhoto, businessType, businessId)
        .then(res => {
          resolve(res.data.photoId)
        })
        .catch(error => {
          console.error('上传人脸照片失败:', error)
          reject(error)
        })
    })
  },

  // 上一步
  prevStep() {
    wx.navigateBack()
  },

  // 下一步
  nextStep() {
    if (!this.data.canProceed) return
    
    const { stepData, recognitionResult, facePhoto } = this.data
    
    // 更新步骤数据
    const updatedStepData = {
      ...stepData,
      faceRecognition: {
        success: true,
        userName: recognitionResult.userName,
        userId: recognitionResult.userId,
        confidence: recognitionResult.confidence,
        verifyTime: recognitionResult.verifyTime,
        facePhotoUrl: facePhoto
      }
    }
    
    // 保存更新后的数据
    wx.setStorageSync('checkinStepData', updatedStepData)
    
    // 跳转到第三步
    wx.navigateTo({
      url: '/pages/maintenance/checkin-step3'
    })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '人脸识别验证 - 运维宝',
      path: '/pages/maintenance/checkin-step2',
      imageUrl: '/images/share-face-recognition.png'
    }
  }
})
