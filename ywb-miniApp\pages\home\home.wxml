<!--pages/home/<USER>
<view class="home-container">
  <!-- 头部标题 -->
  <view class="home-header">
    <text class="header-title">中邮科运维宝</text>
    <view class="header-actions">
      <text class="user-name">{{userInfo.nickname || '用户'}}</text>
    </view>
  </view>

  <!-- 项目管理区域 -->
  <view class="content-section">
    <view class="section-header">
      <text class="section-title">项目管理</text>
      <text class="section-subtitle">共{{projectList.length}}个项目</text>
    </view>

    <!-- 项目列表 -->
    <scroll-view class="project-list" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
      <view class="project-card" wx:for="{{projectList}}" wx:key="id" bindtap="viewProjectDetail" data-project="{{item}}">
        <view class="project-header">
          <text class="project-name">{{item.projectName}}</text>
          <view class="project-status">
            <text class="status-tag {{item.status === 1 ? 'active' : item.status === 2 ? 'completed' : 'inactive'}}">
              {{item.status === 1 ? '进行中' : item.status === 2 ? '已完成' : '已停用'}}
            </text>
          </view>
        </view>
        
        <view class="project-info">
          <text class="project-desc">{{item.projectDesc || '暂无描述'}}</text>
          <text class="project-date">{{item.startDate}} - {{item.endDate || '进行中'}}</text>
        </view>

        <view class="project-stats">
          <view class="stat-item">
            <text class="stat-label">我的交底数</text>
            <text class="stat-value briefing-count">{{item.myBriefingCount || 0}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">我的运维数</text>
            <text class="stat-value maintenance-count">{{item.myMaintenanceCount || 0}}</text>
          </view>
          <view class="project-action">
            <button class="action-btn" bindtap="newMaintenance" data-project="{{item}}" catchtap="true">
              <text class="btn-icon">✎</text>
              <text class="btn-text">新增运维</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{projectList.length === 0 && !loading}}">
        <image class="empty-image" src="/images/empty-project.png" mode="aspectFit"></image>
        <text class="empty-text">暂无项目数据</text>
        <text class="empty-desc">请联系管理员分配项目权限</text>
      </view>

      <!-- 加载状态 -->
      <view class="loading-state" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>
  </view>

  <!-- 快捷操作区域 -->
  <view class="quick-actions" wx:if="{{projectList.length > 0}}">
    <view class="action-item" bindtap="quickSafetyBriefing">
      <view class="action-icon safety-icon">🛡️</view>
      <text class="action-text">安全交底</text>
    </view>
    <view class="action-item" bindtap="quickCheckin">
      <view class="action-icon checkin-icon">📍</view>
      <text class="action-text">快速打卡</text>
    </view>
    <view class="action-item" bindtap="quickSiteManage">
      <view class="action-icon site-icon">🏢</view>
      <text class="action-text">站点管理</text>
    </view>
  </view>

  <!-- 今日概览 -->
  <view class="today-overview" wx:if="{{todayStats}}">
    <view class="overview-header">
      <text class="overview-title">今日概览</text>
      <text class="overview-date">{{todayDate}}</text>
    </view>
    <view class="overview-stats">
      <view class="overview-item">
        <text class="overview-value">{{todayStats.briefingCount}}</text>
        <text class="overview-label">交底次数</text>
      </view>
      <view class="overview-item">
        <text class="overview-value">{{todayStats.checkinCount}}</text>
        <text class="overview-label">打卡次数</text>
      </view>
      <view class="overview-item">
        <text class="overview-value">{{todayStats.siteCount}}</text>
        <text class="overview-label">站点数量</text>
      </view>
      <view class="overview-item">
        <text class="overview-value">{{todayStats.workDuration}}</text>
        <text class="overview-label">工作时长(h)</text>
      </view>
    </view>
  </view>

  <!-- 公告通知 -->
  <view class="notice-section" wx:if="{{noticeList.length > 0}}">
    <view class="notice-header">
      <text class="notice-title">📢 通知公告</text>
    </view>
    <scroll-view class="notice-list" scroll-x="true">
      <view class="notice-item" wx:for="{{noticeList}}" wx:key="id" bindtap="viewNotice" data-notice="{{item}}">
        <text class="notice-content">{{item.content}}</text>
        <text class="notice-time">{{item.createTime}}</text>
      </view>
    </scroll-view>
  </view>
</view>
