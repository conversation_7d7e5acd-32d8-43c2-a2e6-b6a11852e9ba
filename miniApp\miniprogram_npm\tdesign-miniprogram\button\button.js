import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{canIUseFormFieldButton}from"../common/version";import{calcIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-button`;let Button=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-icon`,`${prefix}-class-loading`],this.behaviors=canIUseFormFieldButton()?["wx://form-field-button"]:[],this.properties=props,this.options={multipleSlots:!0},this.data={prefix:prefix,className:"",classPrefix:name},this.observers={"theme, size, plain, block, shape, disabled, loading, variant"(){this.setClass()},icon(t){this.setData({_icon:calcIcon(t,"")})}},this.lifetimes={attached(){this.setClass()}},this.methods={setClass(){const t=[name,`${prefix}-class`,`${name}--${this.data.variant||"base"}`,`${name}--${this.data.theme||"default"}`,`${name}--${this.data.shape||"rectangle"}`,`${name}--size-${this.data.size||"medium"}`];this.data.block&&t.push(`${name}--block`),this.data.disabled&&t.push(`${name}--disabled`),this.data.ghost&&t.push(`${name}--ghost`),this.setData({className:t.join(" ")})},getuserinfo(t){this.triggerEvent("getuserinfo",t.detail)},contact(t){this.triggerEvent("contact",t.detail)},getphonenumber(t){this.triggerEvent("getphonenumber",t.detail)},error(t){this.triggerEvent("error",t.detail)},opensetting(t){this.triggerEvent("opensetting",t.detail)},launchapp(t){this.triggerEvent("launchapp",t.detail)},chooseavatar(t){this.triggerEvent("chooseavatar",t.detail)},agreeprivacyauthorization(t){this.triggerEvent("agreeprivacyauthorization",t.detail)},handleTap(t){this.data.disabled||this.data.loading||this.triggerEvent("tap",t)}}}};Button=__decorate([wxComponent()],Button);export default Button;