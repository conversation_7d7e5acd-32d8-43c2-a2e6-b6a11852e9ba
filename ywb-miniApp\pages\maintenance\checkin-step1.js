// pages/maintenance/checkin-step1.js
const app = getApp()
const { getCurrentLocation, calculateDistance, isInRange, showError, showSuccess } = require('../../utils/util')

Page({
  data: {
    projectId: '',
    briefingId: '',
    siteId: '',
    
    // 表单数据
    workType: '', // repair: 配套维修, tower: 杆塔维修
    selectedSite: null,
    safetyFactors: [], // electric: 涉电, height: 登高
    
    // 定位相关
    currentLocation: null,
    locationStatus: '', // loading, success, error
    distance: 0,
    distanceValid: false,
    
    // 状态
    canProceed: false
  },

  onLoad(options) {
    console.log('上站打卡步骤1加载', options)
    
    const { projectId, briefingId, siteId } = options
    
    if (!projectId) {
      showError('项目参数错误')
      wx.navigateBack()
      return
    }
    
    this.setData({
      projectId,
      briefingId: briefingId || '',
      siteId: siteId || ''
    })
    
    // 如果有预选站点，加载站点信息
    if (siteId) {
      this.loadSiteInfo(siteId)
    }
  },

  // 加载站点信息
  loadSiteInfo(siteId) {
    // TODO: 调用API获取站点详情
    // 暂时使用模拟数据
    const mockSite = {
      id: siteId,
      siteName: '鼓楼区六一路356号机房',
      siteCode: '350103040010001886',
      siteAddress: '福州市鼓楼区六一路356号',
      siteType: '机房',
      longitude: 119.296494,
      latitude: 26.075302
    }
    
    this.setData({ selectedSite: mockSite })
    this.checkCanProceed()
  },

  // 选择作业类型
  selectWorkType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ workType: type })
    this.checkCanProceed()
  },

  // 选择站点
  selectSite() {
    wx.navigateTo({
      url: `/pages/site/site-selection?projectId=${this.data.projectId}&selectMode=true`
    })
  },

  // 新增站点
  addNewSite() {
    wx.navigateTo({
      url: `/pages/site/new-site?projectId=${this.data.projectId}&returnPage=checkin`
    })
  },

  // 切换安全要素
  toggleSafetyFactor(e) {
    const factor = e.currentTarget.dataset.factor
    const { safetyFactors } = this.data
    
    let newFactors = [...safetyFactors]
    
    if (newFactors.includes(factor)) {
      newFactors = newFactors.filter(f => f !== factor)
    } else {
      newFactors.push(factor)
    }
    
    this.setData({ safetyFactors: newFactors })
  },

  // 获取位置
  getLocation() {
    if (this.data.locationStatus === 'loading') return
    
    this.setData({ locationStatus: 'loading' })
    
    getCurrentLocation()
      .then(location => {
        this.setData({
          currentLocation: location,
          locationStatus: 'success'
        })
        
        // 如果已选择站点，计算距离
        if (this.data.selectedSite) {
          this.calculateDistance()
        }
        
        this.checkCanProceed()
        showSuccess('定位成功')
      })
      .catch(error => {
        console.error('获取位置失败:', error)
        this.setData({ locationStatus: 'error' })
        
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '位置权限',
            content: '需要获取您的位置信息进行打卡验证，请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      // 用户开启了位置权限，重新获取位置
                      setTimeout(() => {
                        this.getLocation()
                      }, 1000)
                    }
                  }
                })
              }
            }
          })
        } else {
          showError('获取位置失败，请重试')
        }
      })
  },

  // 计算距离
  calculateDistance() {
    const { currentLocation, selectedSite } = this.data
    
    if (!currentLocation || !selectedSite) return
    
    const distance = Math.round(calculateDistance(
      currentLocation.latitude,
      currentLocation.longitude,
      selectedSite.latitude,
      selectedSite.longitude
    ))
    
    const distanceValid = isInRange(
      currentLocation.latitude,
      currentLocation.longitude,
      selectedSite.latitude,
      selectedSite.longitude,
      500 // 500米范围
    )
    
    this.setData({
      distance,
      distanceValid
    })
    
    this.checkCanProceed()
  },

  // 检查是否可以进入下一步
  checkCanProceed() {
    const {
      workType,
      selectedSite,
      currentLocation,
      distanceValid
    } = this.data
    
    const canProceed = workType !== '' &&
                      selectedSite !== null &&
                      currentLocation !== null &&
                      distanceValid
    
    this.setData({ canProceed })
  },

  // 下一步
  nextStep() {
    if (!this.data.canProceed) return
    
    const {
      projectId,
      briefingId,
      workType,
      selectedSite,
      safetyFactors,
      currentLocation
    } = this.data
    
    // 构建传递给下一步的数据
    const stepData = {
      projectId,
      briefingId,
      workType,
      siteId: selectedSite.id,
      siteName: selectedSite.siteName,
      safetyFactors: safetyFactors.join(','),
      checkinLocation: {
        longitude: currentLocation.longitude,
        latitude: currentLocation.latitude,
        address: currentLocation.address
      }
    }
    
    // 将数据存储到本地，供下一步使用
    wx.setStorageSync('checkinStepData', stepData)
    
    // 跳转到第二步
    wx.navigateTo({
      url: '/pages/maintenance/checkin-step2'
    })
  },

  // 页面显示时的处理
  onShow() {
    // 检查是否从站点选择页面返回
    const selectedSiteData = wx.getStorageSync('selectedSiteForCheckin')
    if (selectedSiteData) {
      this.setData({ selectedSite: selectedSiteData })
      wx.removeStorageSync('selectedSiteForCheckin')
      
      // 如果已有位置信息，重新计算距离
      if (this.data.currentLocation) {
        this.calculateDistance()
      }
      
      this.checkCanProceed()
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '上站打卡 - 运维宝',
      path: `/pages/maintenance/checkin-step1?projectId=${this.data.projectId}`,
      imageUrl: '/images/share-checkin.png'
    }
  }
})
