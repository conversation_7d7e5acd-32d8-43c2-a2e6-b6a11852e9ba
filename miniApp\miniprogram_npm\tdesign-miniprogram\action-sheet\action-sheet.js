import{__decorate}from"tslib";import{chunk}from"../common/utils";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import{ActionSheetTheme,show}from"./show";import props from"./props";import useCustomNavbar from"../mixins/using-custom-navbar";const{prefix:prefix}=config,name=`${prefix}-action-sheet`;let ActionSheet=class extends SuperComponent{constructor(){super(...arguments),this.behaviors=[useCustomNavbar],this.externalClasses=[`${prefix}-class`,`${prefix}-class-content`,`${prefix}-class-cancel`],this.properties=Object.assign({},props),this.data={prefix:prefix,classPrefix:name,gridThemeItems:[],currentSwiperIndex:0,defaultPopUpProps:{},defaultPopUpzIndex:11500},this.controlledProps=[{key:"visible",event:"visible-change"}],this.observers={"visible, items"(e){e&&this.init()}},this.methods={init(){this.memoInitialData(),this.splitGridThemeActions()},memoInitialData(){this.initialData=Object.assign(Object.assign({},this.properties),this.data)},splitGridThemeActions(){this.data.theme===ActionSheetTheme.Grid&&this.setData({gridThemeItems:chunk(this.data.items,this.data.count)})},show(e){this.setData(Object.assign(Object.assign(Object.assign({},this.initialData),e),{visible:!0})),this.splitGridThemeActions(),this.autoClose=!0,this._trigger("visible-change",{visible:!0})},close(){this.triggerEvent("close",{trigger:"command"}),this._trigger("visible-change",{visible:!1})},onPopupVisibleChange({detail:e}){e.visible||(this.triggerEvent("close",{trigger:"overlay"}),this._trigger("visible-change",{visible:!1})),this.autoClose&&(this.setData({visible:!1}),this.autoClose=!1)},onSwiperChange(e){const{current:t}=e.detail;this.setData({currentSwiperIndex:t})},onSelect(e){const{currentSwiperIndex:t,items:i,gridThemeItems:s,count:o,theme:r}=this.data,{index:n}=e.currentTarget.dataset,a=r===ActionSheetTheme.Grid,h=a?s[t][n]:i[n],c=a?n+t*o:n;h&&(this.triggerEvent("selected",{selected:h,index:c}),h.disabled||(this.triggerEvent("close",{trigger:"select"}),this._trigger("visible-change",{visible:!1})))},onCancel(){this.triggerEvent("cancel"),this.autoClose&&(this.setData({visible:!1}),this.autoClose=!1)}}}};ActionSheet.show=show,ActionSheet=__decorate([wxComponent()],ActionSheet);export default ActionSheet;