# API接口调用示例

本文档提供了运维宝小程序中各种API接口的调用示例，帮助开发者快速集成接口功能。

## 基础用法

### 1. 引入API模块

```javascript
const { authAPI, projectAPI, maintenanceAPI, photoAPI, aiAPI, statisticsAPI } = require('../../utils/api')
```

### 2. 基本调用方式

```javascript
// 获取项目列表
projectAPI.getProjectList({ page: 1, pageSize: 10 })
  .then(res => {
    console.log('项目列表:', res.data)
    // 处理成功响应
  })
  .catch(error => {
    console.error('获取项目列表失败:', error)
    // 处理错误
  })
```

## 认证相关接口

### 微信登录

```javascript
// 微信登录
authAPI.wechatLogin(code, encryptedData, iv)
  .then(res => {
    const { token, userInfo } = res.data
    // 保存token和用户信息
    wx.setStorageSync('token', token)
    wx.setStorageSync('userInfo', userInfo)
    app.globalData.token = token
    app.globalData.userInfo = userInfo
  })
  .catch(error => {
    wx.showToast({
      title: '登录失败',
      icon: 'none'
    })
  })
```

### 绑定手机号

```javascript
// 绑定手机号
authAPI.bindPhone(encryptedData, iv)
  .then(res => {
    wx.showToast({
      title: '绑定成功',
      icon: 'success'
    })
    // 更新用户信息
    this.loadUserInfo()
  })
  .catch(error => {
    wx.showToast({
      title: '绑定失败',
      icon: 'none'
    })
  })
```

## 运维记录接口

### 创建运维记录

```javascript
// 创建运维记录
const recordData = {
  projectId: 'project_123',
  siteId: 'site_456',
  workType: 'repair',
  safetyFactors: ['electric', 'height'],
  workContent: '配套设备维修',
  planStartTime: '2024-01-15 09:00:00'
}

maintenanceAPI.createMaintenanceRecord(recordData)
  .then(res => {
    const recordId = res.data.id
    wx.showToast({
      title: '创建成功',
      icon: 'success'
    })
    // 跳转到记录详情页
    wx.navigateTo({
      url: `/pages/maintenance/record-detail?recordId=${recordId}`
    })
  })
  .catch(error => {
    wx.showToast({
      title: '创建失败',
      icon: 'none'
    })
  })
```

### 上站打卡

```javascript
// 上站打卡
const checkinData = {
  recordId: 'record_123',
  location: {
    latitude: 26.0614,
    longitude: 119.3061
  },
  facePhoto: 'temp_face_photo_path',
  scenePhotos: ['temp_scene_photo_1', 'temp_scene_photo_2']
}

maintenanceAPI.checkin(checkinData)
  .then(res => {
    wx.showToast({
      title: '打卡成功',
      icon: 'success'
    })
    // 跳转到作业页面
    wx.redirectTo({
      url: '/pages/maintenance/working'
    })
  })
  .catch(error => {
    if (error.code === 5002) {
      wx.showModal({
        title: '打卡失败',
        content: '距离站点过远，无法打卡',
        showCancel: false
      })
    }
  })
```

## 照片上传接口

### 单张照片上传

```javascript
// 上传单张照片
photoAPI.uploadPhoto(tempFilePath, 'maintenance', recordId)
  .then(res => {
    const photoUrl = res.data.url
    console.log('照片上传成功:', photoUrl)
    // 更新页面数据
    this.setData({
      photoUrl: photoUrl
    })
  })
  .catch(error => {
    wx.showToast({
      title: '上传失败',
      icon: 'none'
    })
  })
```

### 批量照片上传

```javascript
// 批量上传照片
const tempFilePaths = ['temp_path_1', 'temp_path_2', 'temp_path_3']

photoAPI.batchUploadPhotos(tempFilePaths, 'safety_briefing', briefingId)
  .then(res => {
    const photoUrls = res.data.urls
    console.log('批量上传成功:', photoUrls)
    // 更新页面数据
    this.setData({
      photoList: [...this.data.photoList, ...photoUrls]
    })
  })
  .catch(error => {
    wx.showToast({
      title: '上传失败',
      icon: 'none'
    })
  })
```

## AI分析接口

### 人脸识别

```javascript
// 人脸识别
aiAPI.faceRecognition(facePhotoPath, userId)
  .then(res => {
    const { confidence, matched } = res.data
    if (matched && confidence > 0.8) {
      wx.showToast({
        title: '人脸识别成功',
        icon: 'success'
      })
      // 继续后续流程
      this.proceedToNextStep()
    } else {
      wx.showModal({
        title: '识别失败',
        content: '人脸识别失败，请重新拍照',
        showCancel: false
      })
    }
  })
  .catch(error => {
    wx.showToast({
      title: '识别失败',
      icon: 'none'
    })
  })
```

### 安全装备检测

```javascript
// 安全装备检测
aiAPI.safetyDetection(scenePhotoPath)
  .then(res => {
    const { detectedItems, missingItems } = res.data
    
    if (missingItems.length > 0) {
      wx.showModal({
        title: '安全提醒',
        content: `检测到缺失安全装备：${missingItems.join('、')}`,
        confirmText: '我知道了',
        showCancel: false
      })
    } else {
      wx.showToast({
        title: '安全检测通过',
        icon: 'success'
      })
    }
  })
  .catch(error => {
    console.error('安全检测失败:', error)
  })
```

## 统计分析接口

### 获取用户统计

```javascript
// 获取用户统计数据
statisticsAPI.getUserStats(userId)
  .then(res => {
    const { totalCount, monthCount, totalDuration } = res.data
    this.setData({
      userStats: {
        totalCount,
        monthCount,
        totalDuration: this.formatDuration(totalDuration)
      }
    })
  })
  .catch(error => {
    console.error('获取统计数据失败:', error)
  })
```

### 获取运维趋势

```javascript
// 获取运维趋势数据
statisticsAPI.getMaintenanceTrend({ days: 30 })
  .then(res => {
    const trendData = res.data.list
    // 处理图表数据
    const chartData = this.processChartData(trendData)
    this.setData({ chartData })
  })
  .catch(error => {
    console.error('获取趋势数据失败:', error)
  })
```

## 错误处理

### 统一错误处理

```javascript
const handleApiError = (error) => {
  const { code, message } = error
  
  switch (code) {
    case 2001: // TOKEN_INVALID
    case 2002: // TOKEN_EXPIRED
      // 重新登录
      app.logout()
      break
    case 5002: // SITE_TOO_FAR
      wx.showModal({
        title: '位置错误',
        content: '距离站点过远，无法操作',
        showCancel: false
      })
      break
    default:
      wx.showToast({
        title: message || '操作失败',
        icon: 'none'
      })
  }
}

// 使用示例
maintenanceAPI.checkin(data)
  .then(res => {
    // 处理成功
  })
  .catch(handleApiError)
```

### 重试机制

```javascript
// 带重试的API调用
const apiCallWithRetry = async (apiFunction, params, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await apiFunction(params)
      return result
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}

// 使用示例
apiCallWithRetry(photoAPI.uploadPhoto, [tempFilePath, 'maintenance', recordId])
  .then(res => {
    console.log('上传成功:', res.data.url)
  })
  .catch(error => {
    console.error('上传失败:', error)
  })
```

## 缓存策略

### 数据缓存

```javascript
// 带缓存的数据获取
const getCachedData = async (cacheKey, apiFunction, params, expireTime = 5 * 60 * 1000) => {
  const cached = wx.getStorageSync(cacheKey)
  
  if (cached && cached.timestamp && (Date.now() - cached.timestamp < expireTime)) {
    return cached.data
  }
  
  try {
    const res = await apiFunction(params)
    wx.setStorageSync(cacheKey, {
      data: res.data,
      timestamp: Date.now()
    })
    return res.data
  } catch (error) {
    // 如果有缓存数据，返回缓存
    if (cached && cached.data) {
      return cached.data
    }
    throw error
  }
}

// 使用示例
getCachedData('project_list', projectAPI.getProjectList, { page: 1, pageSize: 20 })
  .then(data => {
    this.setData({ projectList: data.list })
  })
  .catch(error => {
    console.error('获取项目列表失败:', error)
  })
```

## 注意事项

1. **Token管理**: 所有需要认证的接口都会自动携带token，无需手动添加
2. **错误处理**: 建议统一处理常见错误码，提供良好的用户体验
3. **加载状态**: API调用时会自动显示loading，但可以通过参数控制
4. **重试机制**: 对于网络不稳定的情况，建议实现重试机制
5. **缓存策略**: 对于不经常变化的数据，建议使用缓存减少网络请求
6. **参数验证**: 在调用API前，建议先验证必要参数的有效性
