import{__awaiter,__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";import config from"../common/config";import{isFunction,isNumber}from"../common/validator";import{debounce,getRect,rpx2px,styles,unitConvert,nextTick,systemInfo}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-guide`;let Guide=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-reference`,`${prefix}-class-popover`,`${prefix}-class-tooltip`,`${prefix}-class-title`,`${prefix}-class-body`,`${prefix}-class-footer`,`${prefix}-class-skip`,`${prefix}-class-next`,`${prefix}-class-back`,`${prefix}-class-finish`],this.properties=props,this.options={pureDataPattern:/^_/,multipleSlots:!0},this.data={prefix:prefix,classPrefix:name,visible:!1,_current:-1,_steps:[],buttonProps:{},referenceStyle:"",popoverStyle:"",title:"",body:"",nonOverlay:!1,modeType:""},this.controlledProps=[{key:"current",event:"change"}],this.observers={"steps, current, showOverlay"(){return __awaiter(this,void 0,void 0,(function*(){this._init()}))}},this.lifetimes={created(){this._init=debounce((()=>this.init()),20),this._getPlacement=this.getPlacement()},attached(){this._init()}},this.methods={init(){var t,e,i,s,a,o,n;return __awaiter(this,void 0,void 0,(function*(){const{steps:r,current:p}=this.properties,{_steps:l,_current:h}=this.data,c=r[p];if(!c)return this.setData({visible:!1});const u="dialog"===(null!==(t=c.mode)&&void 0!==t?t:this.data.mode)?"dialog":"popover",x=null!==(e=c.showOverlay)&&void 0!==e?e:this.data.showOverlay;if(this.setData({nonOverlay:!x,modeType:u}),r!==l||p!==h)if("popover"===u){const t=yield c.element();if(!t)return;const e=rpx2px(null!==(i=c.highlightPadding)&&void 0!==i?i:this.data.highlightPadding),o={top:`${t.top-e}px`,right:`${systemInfo.windowWidth-t.right-e}px`,left:`${t.left-e}px`,width:`${t.width+2*e}px`,height:`${t.height+2*e}px`};this.setData({_steps:this.data.steps,_current:this.data.current,visible:!0,referenceStyle:styles(o),title:null!==(s=c.title)&&void 0!==s?s:"",body:null!==(a=c.body)&&void 0!==a?a:"",buttonProps:this.buttonProps(c,"popover")});const n=yield this.placementOffset(c,o);this.setData({popoverStyle:n})}else this.setData({_steps:this.data.steps,_current:this.data.current,visible:!0,title:null!==(o=c.title)&&void 0!==o?o:"",body:null!==(n=c.body)&&void 0!==n?n:"",buttonProps:this.buttonProps(c,"dialog")})}))},placementOffset({placement:t,offset:e},i){var s,a;return __awaiter(this,void 0,void 0,(function*(){yield nextTick();const o=yield getRect(this,`.${name}__container`),n=null===(a=(s=this._getPlacement)[t])||void 0===a?void 0:a.call(s,o,i,e);return styles(Object.assign({position:"absolute"},n))}))},buttonProps(t,e){var i,s,a,o;let n=null!==(i=t.skipButtonProps)&&void 0!==i?i:this.data.skipButtonProps;const r="popover"===e?"extra-small":"medium";n=Object.assign(Object.assign({theme:"light",content:"跳过",size:r},n),{tClass:`${prefix}-class-skip ${name}__button ${(null==n?void 0:n.class)||""}`,type:"skip"});let p=null!==(s=t.nextButtonProps)&&void 0!==s?s:this.data.nextButtonProps;p=Object.assign(Object.assign({theme:"primary",content:"下一步",size:r},p),{tClass:`${prefix}-class-next ${name}__button ${(null==p?void 0:p.class)||""}`,type:"next"}),p=Object.assign(Object.assign({},p),{content:this.buttonContent(p)});let l=null!==(a=t.backButtonProps)&&void 0!==a?a:this.data.backButtonProps;l=Object.assign(Object.assign({theme:"light",content:"返回",size:r},l),{tClass:`${prefix}-class-back ${name}__button ${(null==l?void 0:l.class)||""}`,type:"back"});let h=null!==(o=t.finishButtonProps)&&void 0!==o?o:this.data.finishButtonProps;return h=Object.assign(Object.assign({theme:"primary",content:"完成",size:r},h),{tClass:`${prefix}-class-finish ${name}__button ${(null==h?void 0:h.class)||""}`,type:"finish"}),h=Object.assign(Object.assign({},h),{content:this.buttonContent(h)}),{skipButton:n,nextButton:p,backButton:l,finishButton:h}},renderCounter(){const{steps:t,current:e,counter:i}=this.data,s=t.length,a=e+1,o=isFunction(i)?i({total:s,current:a}):i;return i?o:`(${a}/${s})`},buttonContent(t){const{hideCounter:e}=this.data;return`${t.content.replace(/ \(.*?\)/,"")} ${e?"":this.renderCounter()}`},onTplButtonTap(t){const{type:e}=t.target.dataset,i={e:t,current:this.data.current,total:this.data.steps.length};switch(e){case"next":this.triggerEvent("next-step-click",Object.assign({next:this.data.current+1},i)),this.setData({current:this.data.current+1});break;case"skip":this.triggerEvent("skip",i),this.setData({current:-1});break;case"back":this.triggerEvent("back",i),this.setData({current:0});break;case"finish":this.triggerEvent("finish",i),this.setData({current:-1})}this.triggerEvent("change",{current:this.data.current})},getPlacement(){const t=rpx2px(32),e=t=>unitConvert(isNumber(null==t?void 0:t[0])?`${null==t?void 0:t[0]}rpx`:(null==t?void 0:t[0])||0),i=t=>unitConvert(isNumber(null==t?void 0:t[1])?`${null==t?void 0:t[1]}rpx`:(null==t?void 0:t[1])||0),s=t=>parseFloat(t.left),a=t=>parseFloat(t.right),o=t=>parseFloat(t.top),n=t=>parseFloat(t.height),r=t=>parseFloat(t.width);return{center:(a,p,l)=>({top:`${Math.max(n(p)+o(p)+t+i(l),1)}px`,left:`${Math.max(r(p)/2+s(p)-a.width/2+e(l),1)}px`}),bottom:(a,p,l)=>({top:`${Math.max(n(p)+o(p)+t+i(l),1)}px`,left:`${Math.max(r(p)/2+s(p)-a.width/2+e(l),1)}px`}),"bottom-left":(a,r,p)=>({top:`${Math.max(n(r)+o(r)+t+i(p),1)}px`,left:`${Math.max(s(r)+e(p),1)}px`}),"bottom-right":(s,r,p)=>({top:`${Math.max(n(r)+o(r)+t+i(p),1)}px`,right:`${Math.max(a(r)-e(p),1)}px`}),left:(s,p,l)=>({top:`${Math.max(n(p)/2+o(p)-s.height/2+i(l),1)}px`,right:`${Math.max(r(p)+a(p)+t-e(l),1)}px`}),"left-top":(s,n,p)=>({top:`${Math.max(o(n)+i(p),1)}px`,right:`${Math.max(r(n)+a(n)+t-e(p),1)}px`}),"left-bottom":(s,p,l)=>({top:`${Math.max(o(p)+n(p)-s.height-i(l),1)}px`,right:`${Math.max(r(p)+a(p)+t-e(l),1)}px`}),right:(a,p,l)=>({top:`${Math.max(n(p)/2+o(p)-a.height/2+i(l),1)}px`,left:`${Math.max(s(p)+r(p)+t+e(l),1)}px`}),"right-top":(a,n,p)=>({top:`${Math.max(o(n)+i(p),1)}px`,left:`${Math.max(s(n)+r(n)+t+e(p),1)}px`}),"right-bottom":(a,p,l)=>({top:`${Math.max(o(p)+n(p)-a.height-i(l),1)}px`,left:`${Math.max(s(p)+r(p)+t+e(l),1)}px`}),top:(a,n,p)=>({top:`${Math.max(o(n)-a.height-t+i(p),1)}px`,left:`${Math.max(r(n)/2+s(n)-a.width/2+e(p),1)}px`}),"top-left":(a,n,r)=>({top:`${Math.max(o(n)-a.height-t+i(r),1)}px`,left:`${Math.max(s(n)+e(r),1)}px`}),"top-right":(s,n,r)=>({top:`${Math.max(o(n)-s.height-t+i(r),1)}px`,right:`${Math.max(a(n)-e(r),1)}px`})}}}}};Guide=__decorate([wxComponent()],Guide);export default Guide;