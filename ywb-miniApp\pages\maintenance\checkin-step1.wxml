<!--pages/maintenance/checkin-step1.wxml-->
<view class="checkin-container">
  <!-- 步骤条 -->
  <view class="steps-container">
    <view class="steps">
      <view class="step active">
        <view class="step-icon">1</view>
        <text class="step-title">作业内容</text>
      </view>
      <view class="step-line"></view>
      <view class="step">
        <view class="step-icon">2</view>
        <text class="step-title">人脸识别</text>
      </view>
      <view class="step-line"></view>
      <view class="step">
        <view class="step-icon">3</view>
        <text class="step-title">现场交底</text>
      </view>
    </view>
  </view>

  <!-- 表单内容 -->
  <view class="form-container">
    <!-- 作业类型 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">作业类型</text>
        <text class="required-mark">*</text>
      </view>
      <view class="radio-group">
        <view 
          class="radio-item {{workType === 'repair' ? 'selected' : ''}}"
          bindtap="selectWorkType"
          data-type="repair"
        >
          <view class="radio-icon">
            <text class="radio-dot" wx:if="{{workType === 'repair'}}">●</text>
          </view>
          <text class="radio-text">配套维修</text>
        </view>
        <view 
          class="radio-item {{workType === 'tower' ? 'selected' : ''}}"
          bindtap="selectWorkType"
          data-type="tower"
        >
          <view class="radio-icon">
            <text class="radio-dot" wx:if="{{workType === 'tower'}}">●</text>
          </view>
          <text class="radio-text">杆塔维修</text>
        </view>
      </view>
    </view>

    <!-- 站点选择 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">作业站点</text>
        <text class="required-mark">*</text>
      </view>
      <view class="site-selector">
        <view class="site-input" bindtap="selectSite">
          <text class="{{selectedSite ? 'site-name' : 'placeholder'}}">
            {{selectedSite ? selectedSite.siteName : '请选择作业站点'}}
          </text>
          <text class="selector-arrow">></text>
        </view>
        <view class="site-info" wx:if="{{selectedSite}}">
          <text class="site-code">站点编码：{{selectedSite.siteCode}}</text>
          <text class="site-address">站点地址：{{selectedSite.siteAddress}}</text>
          <text class="site-type">站点类型：{{selectedSite.siteType}}</text>
        </view>
      </view>
      <view class="new-site-btn" bindtap="addNewSite">
        <text class="btn-icon">+</text>
        <text class="btn-text">新增站点</text>
      </view>
    </view>

    <!-- 安全要素 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">安全要素</text>
      </view>
      <view class="checkbox-group">
        <view 
          class="checkbox-item {{safetyFactors.includes('electric') ? 'checked' : ''}}"
          bindtap="toggleSafetyFactor"
          data-factor="electric"
        >
          <view class="checkbox-icon">
            <text class="check-mark" wx:if="{{safetyFactors.includes('electric')}}">✓</text>
          </view>
          <text class="checkbox-text">涉电</text>
        </view>
        <view 
          class="checkbox-item {{safetyFactors.includes('height') ? 'checked' : ''}}"
          bindtap="toggleSafetyFactor"
          data-factor="height"
        >
          <view class="checkbox-icon">
            <text class="check-mark" wx:if="{{safetyFactors.includes('height')}}">✓</text>
          </view>
          <text class="checkbox-text">登高</text>
        </view>
      </view>
    </view>

    <!-- 定位打卡 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">定位打卡</text>
        <text class="required-mark">*</text>
      </view>
      <view class="location-section">
        <button 
          class="location-btn {{locationStatus === 'success' ? 'success' : locationStatus === 'loading' ? 'loading' : ''}}"
          bindtap="getLocation"
          disabled="{{locationStatus === 'loading'}}"
        >
          <text class="btn-icon">
            {{locationStatus === 'success' ? '✓' : locationStatus === 'loading' ? '⟳' : '📍'}}
          </text>
          <text class="btn-text">
            {{locationStatus === 'success' ? '定位成功' : locationStatus === 'loading' ? '定位中...' : '获取位置'}}
          </text>
        </button>
        
        <view class="location-info" wx:if="{{currentLocation}}">
          <text class="location-address">{{currentLocation.address}}</text>
          <text class="location-coords">
            经度：{{currentLocation.longitude}} 纬度：{{currentLocation.latitude}}
          </text>
          <view class="distance-check {{distanceValid ? 'valid' : 'invalid'}}" wx:if="{{selectedSite && currentLocation}}">
            <text class="distance-icon">{{distanceValid ? '✓' : '⚠'}}</text>
            <text class="distance-text">
              {{distanceValid ? '距离站点' + distance + 'm，可以打卡' : '距离站点' + distance + 'm，超出500m范围'}}
            </text>
          </view>
        </view>
        
        <view class="location-tip">
          <text class="tip-text">💡 打卡需在作业站点500米范围内</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button 
      class="next-btn {{canProceed ? '' : 'disabled'}}"
      bindtap="nextStep"
      disabled="{{!canProceed}}"
    >
      <text class="btn-text">下一步</text>
      <text class="btn-arrow">→</text>
    </button>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-overlay" wx:if="{{locationStatus === 'loading'}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在获取位置信息...</text>
    </view>
  </view>
</view>
