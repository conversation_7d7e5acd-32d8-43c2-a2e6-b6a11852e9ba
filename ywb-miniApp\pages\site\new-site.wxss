/* pages/site/new-site.wxss */
.new-site-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 表单区域 */
.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  margin-bottom: 24rpx;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 24rpx;
  color: #666;
}

.form-group {
  margin-bottom: 32rpx;
  position: relative;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-label.required::before {
  content: '*';
  color: #f44336;
  margin-right: 8rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  color: #333;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #1976d2;
  outline: none;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

.form-textarea {
  min-height: 120rpx;
  line-height: 1.6;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  font-size: 28rpx;
  color: #333;
}

.form-picker.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.input-counter {
  position: absolute;
  right: 16rpx;
  bottom: -32rpx;
  font-size: 24rpx;
  color: #999;
}

/* 位置信息 */
.location-group {
  margin-bottom: 32rpx;
}

.location-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: 2rpx solid #e0e0e0;
  background: #fff;
  color: #333;
  transition: all 0.3s ease;
  margin-bottom: 24rpx;
}

.location-btn:not([disabled]):active {
  border-color: #1976d2;
  background: #f0f8ff;
}

.location-btn.success {
  border-color: #4caf50;
  background: #e8f5e8;
  color: #4caf50;
}

.location-btn.loading {
  border-color: #ff9800;
  background: #fff3e0;
  color: #ff9800;
}

.location-btn[disabled] {
  opacity: 0.6;
}

.btn-icon {
  font-size: 32rpx;
}

.location-info {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #1976d2;
}

.location-address {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.location-coords {
  display: flex;
  gap: 32rpx;
}

.coord-item {
  font-size: 24rpx;
  color: #666;
  font-family: 'Courier New', monospace;
}

.manual-input-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.manual-title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  text-align: center;
}

.coord-inputs {
  display: flex;
  gap: 16rpx;
}

.coord-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.coord-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.coord-input {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background: #fff;
  color: #333;
  font-family: 'Courier New', monospace;
}

.coord-input:focus {
  border-color: #1976d2;
  outline: none;
}

.coord-input::placeholder {
  color: #999;
}

/* 照片上传 */
.photo-upload-area {
  margin-top: 16rpx;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.photo-add-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.photo-add-btn:active {
  background: #f0f0f0;
  border-color: #1976d2;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.add-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 提交区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn:not(.disabled) {
  background: #1976d2;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.submit-btn:not(.disabled):active {
  background: #1565c0;
  transform: translateY(2rpx);
}

.submit-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-text {
  font-size: 32rpx;
}

/* 提交遮罩 */
.submit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.submit-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 32rpx;
}

.submit-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submit-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.submit-tip {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .photo-item,
  .photo-add-btn {
    width: 160rpx;
    height: 160rpx;
  }
  
  .add-icon {
    font-size: 40rpx;
  }
  
  .add-text {
    font-size: 22rpx;
  }
  
  .coord-inputs {
    flex-direction: column;
  }
}
