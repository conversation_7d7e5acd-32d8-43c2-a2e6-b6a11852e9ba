<wxs src="./calendar.wxs" module="_this"/><wxs src="../common/utils.wxs" module="_"/><import src="../common/template/button.wxml"/><import src="./calendar-header.wxml"/><view class="{{_.cls(classPrefix, [['popup', usePopup]])}} {{classPrefix}}-switch-mode--{{switchMode}} class {{prefix}}-class" style="{{_._style([style, customStyle])}}"><view class="{{classPrefix}}__title" tabindex="0"><slot name="title"/><text wx:if="{{title || realLocalText.title}}">{{ title || realLocalText.title }}</text></view><t-icon wx:if="{{usePopup}}" name="close" class="{{classPrefix}}__close-btn" size="48rpx" aria-role="button" aria-label="关闭" bind:tap="handleClose"/><template wx:if="{{switchMode !== 'none'}}" is="calendar-header" data="{{ classPrefix: classPrefix + '-header', switchMode, ...actionButtons, title: _this.getMonthTitle(currentMonth[0].year, realLocalText.months[currentMonth[0].month], realLocalText.monthTitle)}}"/><view aria-hidden class="{{classPrefix}}__days"><view wx:for="{{days}}" wx:key="index" class="{{classPrefix}}__days-item">{{ item }}</view></view><scroll-view class="{{classPrefix}}__months" scroll-into-view="{{scrollIntoView}}" scroll-y enhanced show-scrollbar="{{false}}" bindscroll="onScroll"><block wx:for="{{ switchMode === 'none' ? months : currentMonth}}" wx:key="index"><template wx:if="{{switchMode === 'none'}}" is="calendar-header" data="{{class: classPrefix + '__month', classPrefix: classPrefix + '-header', tId: 'year_' + item.year + '_month_' + item.month, switchMode, ...actionButtons, title: _this.getMonthTitle(item.year, realLocalText.months[item.month], realLocalText.monthTitle) }}"/><view class="{{classPrefix}}__dates"><view wx:for="{{(item.weekdayOfFirstDay - firstDayOfWeek + 7) % 7}}" wx:key="index"/><block wx:for="{{item.months}}" wx:for-index="dateIndex" wx:for-item="dateItem" wx:key="dateIndex"><view class="{{classPrefix}}__dates-item {{dateItem.className}} {{classPrefix}}__dates-item--{{dateItem.type}}" data-year="{{item.year}}" data-month="{{item.month}}" data-date="{{dateItem}}" aria-role="button" aria-label="{{_this.getDateLabel(item, dateItem)}}" aria-disabled="{{dateItem.type === 'disabled'}}" bind:tap="handleSelect"><view wx:if="{{dateItem.prefix}}" class="{{classPrefix}}__dates-item-prefix">{{ dateItem.prefix }}</view>{{ dateItem.day }}<view wx:if="{{dateItem.suffix}}" class="{{classPrefix}}__dates-item-suffix {{classPrefix}}__dates-item-suffix--{{dateItem.type}}">{{ dateItem.suffix }}</view></view></block></view></block></scroll-view><view wx:if="{{innerConfirmBtn != null && usePopup}}" class="{{classPrefix}}__footer"><slot wx:if="{{innerConfirmBtn === 'slot'}}" name="confirm-btn"/><block wx:elif="{{innerConfirmBtn}}"><template is="button" data="{{ block: true,  theme: 'primary', rootClass: 't-calendar__confirm-btn', content: realLocalText.confirm, ...innerConfirmBtn }}"/></block></view></view>