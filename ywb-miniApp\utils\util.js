// utils/util.js
// 工具函数

/**
 * 格式化时间
 * @param {Date} date 日期对象
 * @param {string} format 格式化字符串，如 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 获取当前时间字符串
 * @param {string} format 格式化字符串
 * @returns {string} 当前时间字符串
 */
const getCurrentTime = (format = 'YYYY-MM-DD HH:mm:ss') => {
  return formatTime(new Date(), format)
}

/**
 * 计算两个时间点之间的时长（分钟）
 * @param {string|Date} startTime 开始时间
 * @param {string|Date} endTime 结束时间
 * @returns {number} 时长（分钟）
 */
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return 0
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  return Math.round((end - start) / (1000 * 60))
}

/**
 * 格式化时长显示
 * @param {number} minutes 分钟数
 * @returns {string} 格式化后的时长字符串
 */
const formatDuration = (minutes) => {
  if (!minutes || minutes < 0) return '0分钟'
  
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  } else {
    return `${mins}分钟`
  }
}

/**
 * 计算两个地理坐标之间的距离（米）
 * @param {number} lat1 纬度1
 * @param {number} lng1 经度1
 * @param {number} lat2 纬度2
 * @param {number} lng2 经度2
 * @returns {number} 距离（米）
 */
const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371000 // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 获取当前位置
 * @returns {Promise} 位置信息
 */
const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          address: res.address || ''
        })
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        
        if (error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '位置权限',
            content: '需要获取您的位置信息进行打卡验证，请在设置中开启位置权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting()
              }
            }
          })
        } else {
          wx.showToast({
            title: '获取位置失败',
            icon: 'none'
          })
        }
        
        reject(error)
      }
    })
  })
}

/**
 * 验证是否在指定范围内
 * @param {number} currentLat 当前纬度
 * @param {number} currentLng 当前经度
 * @param {number} targetLat 目标纬度
 * @param {number} targetLng 目标经度
 * @param {number} range 允许范围（米），默认500米
 * @returns {boolean} 是否在范围内
 */
const isInRange = (currentLat, currentLng, targetLat, targetLng, range = 500) => {
  const distance = calculateDistance(currentLat, currentLng, targetLat, targetLng)
  return distance <= range
}

/**
 * 选择图片
 * @param {number} count 最大选择数量
 * @param {Array} sizeType 图片尺寸类型
 * @param {Array} sourceType 图片来源
 * @returns {Promise} 选择的图片信息
 */
const chooseImage = (count = 1, sizeType = ['compressed'], sourceType = ['camera', 'album']) => {
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      count,
      sizeType,
      sourceType,
      success: (res) => {
        resolve(res.tempFilePaths)
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        
        if (error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '相机权限',
            content: '需要使用相机进行拍照，请在设置中开启相机权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting()
              }
            }
          })
        } else {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
        
        reject(error)
      }
    })
  })
}

/**
 * 预览图片
 * @param {Array} urls 图片链接数组
 * @param {number} current 当前显示图片的索引
 */
const previewImage = (urls, current = 0) => {
  wx.previewImage({
    urls,
    current: urls[current] || urls[0]
  })
}

/**
 * 显示加载提示
 * @param {string} title 提示文字
 * @param {boolean} mask 是否显示透明蒙层
 */
const showLoading = (title = '加载中...', mask = true) => {
  wx.showLoading({
    title,
    mask
  })
}

/**
 * 隐藏加载提示
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 显示成功提示
 * @param {string} title 提示文字
 * @param {number} duration 显示时长
 */
const showSuccess = (title, duration = 2000) => {
  wx.showToast({
    title,
    icon: 'success',
    duration
  })
}

/**
 * 显示错误提示
 * @param {string} title 提示文字
 * @param {number} duration 显示时长
 */
const showError = (title, duration = 2000) => {
  wx.showToast({
    title,
    icon: 'none',
    duration
  })
}

/**
 * 显示确认对话框
 * @param {string} title 标题
 * @param {string} content 内容
 * @param {string} confirmText 确认按钮文字
 * @param {string} cancelText 取消按钮文字
 * @returns {Promise} 用户选择结果
 */
const showConfirm = (title, content, confirmText = '确定', cancelText = '取消') => {
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      confirmText,
      cancelText,
      success: (res) => {
        resolve(res.confirm)
      }
    })
  })
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
const debounce = (func, delay = 300) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
const throttle = (func, delay = 300) => {
  let timer = null
  return function (...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
const validatePhone = (phone) => {
  const phoneReg = /^1[3-9]\d{9}$/
  return phoneReg.test(phone)
}

/**
 * 验证身份证号格式
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
const validateIdCard = (idCard) => {
  const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardReg.test(idCard)
}

module.exports = {
  formatTime,
  getCurrentTime,
  calculateDuration,
  formatDuration,
  calculateDistance,
  getCurrentLocation,
  isInRange,
  chooseImage,
  previewImage,
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  showConfirm,
  debounce,
  throttle,
  deepClone,
  generateId,
  validatePhone,
  validateIdCard
}
