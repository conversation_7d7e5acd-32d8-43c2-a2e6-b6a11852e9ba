<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-avatar">
      <image 
        class="avatar-image" 
        src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" 
        mode="aspectFill"
      />
    </view>
    <view class="user-info">
      <text class="user-name">{{userInfo.nickname || userInfo.username || '未设置昵称'}}</text>
      <text class="user-phone">{{userInfo.phone || '未绑定手机'}}</text>
      <text class="user-id">工号：{{userInfo.employeeId || '未设置'}}</text>
    </view>
    <view class="user-actions">
      <button class="edit-btn" bindtap="editProfile">
        <text class="btn-text">编辑</text>
      </button>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-section">
    <view class="stats-item" bindtap="goToHistory">
      <text class="stats-number">{{userStats.totalCount || 0}}</text>
      <text class="stats-label">总运维次数</text>
    </view>
    <view class="stats-item" bindtap="goToMyStats">
      <text class="stats-number">{{userStats.monthCount || 0}}</text>
      <text class="stats-label">本月运维</text>
    </view>
    <view class="stats-item" bindtap="goToMyStats">
      <text class="stats-number">{{userStats.totalDuration || '0小时'}}</text>
      <text class="stats-label">累计时长</text>
    </view>
  </view>

  <!-- 功能菜单 */
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToMyStats">
        <view class="menu-icon stats">
          <text class="icon-text">📊</text>
        </view>
        <text class="menu-label">我的统计</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="goToHistory">
        <view class="menu-icon history">
          <text class="icon-text">📋</text>
        </view>
        <text class="menu-label">历史记录</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-icon settings">
          <text class="icon-text">⚙️</text>
        </view>
        <text class="menu-label">设置</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="contactService">
        <view class="menu-icon service">
          <text class="icon-text">💬</text>
        </view>
        <text class="menu-label">联系客服</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="showFeedback">
        <view class="menu-icon feedback">
          <text class="icon-text">💡</text>
        </view>
        <text class="menu-label">意见反馈</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="showAbout">
        <view class="menu-icon about">
          <text class="icon-text">ℹ️</text>
        </view>
        <text class="menu-label">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">
      <text class="btn-text">退出登录</text>
    </button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">运维宝 v{{appVersion}}</text>
  </view>
</view>

<!-- 意见反馈弹窗 -->
<view class="feedback-modal" wx:if="{{showFeedbackModal}}">
  <view class="modal-mask" bindtap="hideFeedback"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">意见反馈</text>
      <text class="modal-close" bindtap="hideFeedback">×</text>
    </view>
    
    <view class="modal-body">
      <textarea 
        class="feedback-textarea" 
        placeholder="请输入您的意见或建议（上限500字）"
        value="{{feedbackContent}}"
        bindinput="onFeedbackInput"
        maxlength="500"
        auto-height
      />
      <text class="feedback-counter">{{feedbackContent.length}}/500</text>
      
      <view class="contact-info">
        <text class="contact-label">联系方式（选填）</text>
        <input 
          class="contact-input" 
          placeholder="请输入手机号或邮箱"
          value="{{feedbackContact}}"
          bindinput="onContactInput"
        />
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideFeedback">取消</button>
      <button 
        class="submit-btn {{feedbackContent.trim() ? '' : 'disabled'}}" 
        bindtap="submitFeedback"
        disabled="{{!feedbackContent.trim() || isSubmittingFeedback}}"
      >
        {{isSubmittingFeedback ? '提交中...' : '提交'}}
      </button>
    </view>
  </view>
</view>

<!-- 关于我们弹窗 -->
<view class="about-modal" wx:if="{{showAboutModal}}">
  <view class="modal-mask" bindtap="hideAbout"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">关于运维宝</text>
      <text class="modal-close" bindtap="hideAbout">×</text>
    </view>
    
    <view class="modal-body">
      <view class="about-content">
        <view class="app-logo">
          <image class="logo-image" src="/images/logo.png" mode="aspectFit" />
        </view>
        
        <text class="app-name">运维宝</text>
        <text class="app-version">版本 {{appVersion}}</text>
        
        <view class="app-description">
          <text class="desc-text">运维宝是专为电力运维人员打造的移动办公平台，提供智能化的运维管理、安全交底、现场打卡等功能，助力提升运维工作效率和安全管理水平。</text>
        </view>
        
        <view class="company-info">
          <text class="company-name">福建省电力有限公司</text>
          <text class="copyright">© 2024 版权所有</text>
        </view>
      </view>
    </view>
  </view>
</view>
