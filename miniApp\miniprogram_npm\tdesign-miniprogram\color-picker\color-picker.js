import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{SATURATION_PANEL_DEFAULT_HEIGHT,SATURATION_PANEL_DEFAULT_WIDTH,SLIDER_DEFAULT_WIDTH,DEFAULT_COLOR,ALPHA_MAX,HUE_MAX,DEFAULT_SYSTEM_SWATCH_COLORS}from"./constants";import{getRect,debounce}from"../common/utils";import{Color,getColorObject}from"./utils";const{prefix:prefix}=config,name=`${prefix}-color-picker`,getCoordinate=(t,e,a)=>{var o;const{pageX:i,pageY:r,clientY:l}=t.changedTouches[0]||{},s=a?e.top:null===(o=t.currentTarget)||void 0===o?void 0:o.offsetTop;return{x:Math.min(Math.max(0,i-e.left),e.width),y:Math.min(Math.max(0,(a?l:r)-s),e.height)}},getFormatList=(t,e)=>{const a={HSV:Object.values(e.getHsva()),HSVA:Object.values(e.getHsva()),HSL:Object.values(e.getHsla()),HSLA:Object.values(e.getHsla()),HSB:Object.values(e.getHsla()),RGB:Object.values(e.getRgba()),RGBA:Object.values(e.getRgba()),CMYK:[...Object.values(e.getCmyk()),0],CSS:[e.css,0],HEX:[e.hex,0]},o=a[t];return o?[...o.slice(0,o.length-1),`${Math.round(100*e.alpha)}%`]:a.RGB},genSwatchList=t=>void 0===t?DEFAULT_SYSTEM_SWATCH_COLORS:t&&t.length?t:[];let ColorPicker=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.properties=props,this.observers={format(){this.setCoreStyle()},swatchColors(t){this.setData({innerSwatchList:genSwatchList(t)})},type(t){this.setData({isMultiple:"multiple"===t})},"usePopup, visible"(t,e){this.timer&&clearTimeout(this.timer),t&&e&&(this.timer=setTimeout((()=>{this.getEleReact()}),350))},value(t){t&&this.init()}},this.color=new Color(props.defaultValue.value||props.value.value||DEFAULT_COLOR),this.data={prefix:prefix,classPrefix:name,panelRect:{width:SATURATION_PANEL_DEFAULT_WIDTH,height:SATURATION_PANEL_DEFAULT_HEIGHT},sliderRect:{width:SLIDER_DEFAULT_WIDTH,left:0},saturationInfo:{saturation:0,value:0},saturationThumbStyle:{left:0,top:0},sliderInfo:{value:0},hueSliderStyle:{left:0},alphaSliderStyle:{left:0},innerValue:props.defaultValue.value||props.value.value,showPrimaryColorPreview:!1,previewColor:props.defaultValue.value||props.value.value,formatList:getFormatList(props.format.value,this.color),innerSwatchList:genSwatchList(props.swatchColors.value),isMultiple:"multiple"===props.type.value,defaultOverlayProps:{}},this.lifetimes={ready(){this.init()},attached(){this.debouncedUpdateEleRect=debounce((t=>this.updateEleRect(t)),150)},detached(){clearTimeout(this.timer)}},this.methods={init(){const{value:t,defaultValue:e}=this.properties,a=t||e;a&&this.setData({innerValue:a}),this.color=new Color(a||DEFAULT_COLOR),this.updateColor(),this.getEleReact()},updateEleRect(t){if(!t)return;const{scrollTop:e}=t.detail,{width:a,height:o,left:i,initTop:r}=this.data.panelRect;this.setData({panelRect:{width:a,height:o,left:i,top:r-e,initTop:r}})},getEleReact(){Promise.all([getRect(this,`.${name}__saturation`),getRect(this,`.${name}__slider`)]).then((([t,e])=>{this.setData({panelRect:{width:t.width||SATURATION_PANEL_DEFAULT_WIDTH,height:t.height||SATURATION_PANEL_DEFAULT_HEIGHT,left:t.left||0,top:t.top||0,initTop:t.top||0},sliderRect:{left:e.left||0,width:e.width||SLIDER_DEFAULT_WIDTH}},(()=>{this.setCoreStyle()}))}))},clickSwatch(t){const e=t.currentTarget.dataset.value;this.color.update(e),this.emitColorChange("preset"),this.setCoreStyle()},setCoreStyle(){this.setData({sliderInfo:{value:this.color.hue},hueSliderStyle:this.getSliderThumbStyle({value:this.color.hue,maxValue:HUE_MAX}),alphaSliderStyle:this.getSliderThumbStyle({value:100*this.color.alpha,maxValue:ALPHA_MAX}),saturationInfo:{saturation:this.color.saturation,value:this.color.value},saturationThumbStyle:this.getSaturationThumbStyle({saturation:this.color.saturation,value:this.color.value}),previewColor:this.color.rgba,formatList:getFormatList(this.properties.format,this.color)})},emitColorChange(t){this.setData({innerValue:this.formatValue()}),this.triggerEvent("change",{value:this.formatValue(),context:{trigger:t,color:getColorObject(this.color)}})},defaultEmptyColor:()=>DEFAULT_COLOR,updateColor(){const t=this.data.innerValue||this.defaultEmptyColor();this.color.update(t)},getSaturationAndValueByCoordinate(t){const{width:e,height:a}=this.data.panelRect,{x:o,y:i}=t;let r=o/e,l=1-i/a;return r=Math.min(1,Math.max(0,r)),l=Math.min(1,Math.max(0,l)),{saturation:r,value:l}},getSaturationThumbStyle({saturation:t,value:e}){const{width:a,height:o}=this.data.panelRect,i=Math.round((1-e)*o),r=Math.round(t*a);return{color:this.color.rgb,left:`${r}px`,top:`${i}px`}},getSliderThumbStyle({value:t,maxValue:e}){const{width:a}=this.data.sliderRect;if(!a)return;return{left:`${Math.round(t/e*100)}%`,color:this.color.rgb}},onChangeSaturation({saturation:t,value:e}){const{saturation:a,value:o}=this.color;let i="palette-saturation-brightness";if(e!==o&&t!==a)this.color.saturation=t,this.color.value=e,i="palette-saturation-brightness";else if(t!==a)this.color.saturation=t,i="palette-saturation";else{if(e===o)return;this.color.value=e,i="palette-brightness"}this.triggerEvent("palette-bar-change",{color:getColorObject(this.color)}),this.emitColorChange(i),this.setCoreStyle()},formatValue(){return this.color.getFormatsColorMap()[this.properties.format]||this.color.css},onChangeSlider({value:t,isAlpha:e}){e?this.color.alpha=t/100:this.color.hue=t,this.emitColorChange(e?"palette-alpha-bar":"palette-hue-bar"),this.setCoreStyle()},handleSaturationDrag(t){const{usePopup:e,fixed:a}=this.properties,o=getCoordinate(t,this.data.panelRect,e||a),{saturation:i,value:r}=this.getSaturationAndValueByCoordinate(o);this.onChangeSaturation({saturation:i,value:r})},handleSliderDrag(t,e=!1){const{width:a}=this.data.sliderRect,o=getCoordinate(t,this.data.sliderRect),{x:i}=o,r=e?ALPHA_MAX:HUE_MAX;let l=Math.round(i/a*r*100)/100;l<0&&(l=0),l>r&&(l=r),this.onChangeSlider({value:l,isAlpha:e})},handleDiffDrag(t){switch(t.target.dataset.type||t.currentTarget.dataset.type){case"saturation":this.handleSaturationDrag(t);break;case"hue-slider":this.handleSliderDrag(t);break;case"alpha-slider":this.handleSliderDrag(t,!0)}},onTouchStart(t){this.handleDiffDrag(t)},onTouchMove(t){this.handleDiffDrag(t)},onTouchEnd(t){wx.nextTick((()=>{this.handleDiffDrag(t)}))},close(t){this.properties.autoClose&&this.setData({visible:!1}),this.triggerEvent("close",{trigger:t})},onVisibleChange(){this.close("overlay")}}}};ColorPicker=__decorate([wxComponent()],ColorPicker);export default ColorPicker;