/* pages/maintenance/checkin-step3.wxss */
.checkin-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 步骤条样式（复用前面的样式） */
.steps-container {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  background: #e0e0e0;
  color: #999;
  transition: all 0.3s ease;
}

.step.active .step-icon {
  background: #1976d2;
  color: #fff;
  transform: scale(1.1);
}

.step.completed .step-icon {
  background: #4caf50;
  color: #fff;
}

.step-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.step.active .step-title {
  color: #1976d2;
  font-weight: 600;
}

.step.completed .step-title {
  color: #4caf50;
  font-weight: 600;
}

.step-line {
  position: absolute;
  top: 32rpx;
  left: 50%;
  width: 100%;
  height: 2rpx;
  background: #e0e0e0;
  z-index: -1;
}

.step:last-child .step-line {
  display: none;
}

.step-line.completed {
  background: #4caf50;
}

/* 内容容器 */
.content-container {
  padding: 32rpx;
}

/* 信息确认区域 */
.info-confirm-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.required-mark {
  color: #f44336;
  font-size: 32rpx;
  margin-left: 8rpx;
}

.info-card {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #1976d2;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  word-break: break-all;
}

/* 照片区域 */
.photo-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.photo-group {
  margin-bottom: 40rpx;
}

.photo-group:last-child {
  margin-bottom: 0;
}

.photo-header {
  margin-bottom: 20rpx;
}

.photo-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.photo-desc {
  font-size: 24rpx;
  color: #666;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.photo-add-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.photo-add-btn:active {
  background: #f0f0f0;
  border-color: #1976d2;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.add-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 备注区域 */
.remark-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.remark-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  color: #333;
  line-height: 1.6;
}

.remark-textarea:focus {
  border-color: #1976d2;
  outline: none;
}

.remark-textarea::placeholder {
  color: #999;
}

.input-counter {
  position: absolute;
  right: 48rpx;
  bottom: 16rpx;
  font-size: 24rpx;
  color: #999;
}

/* AI检测结果 */
.ai-detection-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.detection-results {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detection-item {
  padding: 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
}

.detection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detection-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.detection-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.detection-status.pass {
  background: #e8f5e8;
  color: #4caf50;
}

.detection-status.warning {
  background: #fff3e0;
  color: #ff9800;
}

.detection-status.fail {
  background: #ffebee;
  color: #f44336;
}

.status-icon {
  font-size: 20rpx;
}

.detection-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
}

.back-btn,
.complete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.back-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.back-btn:active {
  background: #e0e0e0;
}

.complete-btn {
  flex: 2;
}

.complete-btn:not(.disabled) {
  background: #4caf50;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

.complete-btn:not(.disabled):active {
  background: #45a049;
  transform: translateY(2rpx);
}

.complete-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-arrow {
  font-size: 28rpx;
}

/* 提交遮罩 */
.submit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.submit-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 32rpx;
}

.submit-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4caf50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submit-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.submit-tip {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .photo-item,
  .photo-add-btn {
    width: 160rpx;
    height: 160rpx;
  }

  .add-icon {
    font-size: 40rpx;
  }

  .add-text {
    font-size: 22rpx;
  }
}
