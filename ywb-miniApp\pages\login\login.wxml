<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 头部 -->
  <view class="login-header">
    <view class="back-btn" wx:if="{{showBack}}" bindtap="goBack">
      <text class="iconfont icon-back"></text>
    </view>
    <text class="header-title">请登录</text>
  </view>

  <!-- 主要内容 -->
  <view class="login-content">
    <!-- LOGO区域 -->
    <view class="logo-section">
      <view class="logo-container">
        <image class="logo-image" src="/images/logo.png" mode="aspectFit"></image>
      </view>
      <view class="app-name">运维宝</view>
    </view>

    <!-- 登录按钮区域 -->
    <view class="login-actions">
      <button 
        class="login-btn wechat-login-btn" 
        bindtap="handleWechatLogin"
        disabled="{{isLoading}}"
      >
        <text class="btn-icon">🔐</text>
        <text class="btn-text">{{isLoading ? '登录中...' : '微信账号快捷登录'}}</text>
      </button>
    </view>

    <!-- 提示信息 -->
    <view class="login-tips">
      <text class="tip-text">本小程序内部使用，目前并不对外开放，</text>
      <text class="tip-text">感谢您的支持与理解！</text>
    </view>

    <!-- 手机号绑定弹窗 -->
    <view class="phone-modal {{showPhoneModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
      <view class="modal-mask" bindtap="closePhoneModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">绑定手机号</text>
          <text class="modal-close" bindtap="closePhoneModal">×</text>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">手机号码</text>
            <input 
              class="form-input" 
              type="number" 
              placeholder="请输入手机号码"
              value="{{phoneNumber}}"
              bindinput="onPhoneInput"
              maxlength="11"
            />
          </view>
          <view class="form-group">
            <text class="form-label">验证码</text>
            <view class="code-input-group">
              <input 
                class="form-input code-input" 
                type="number" 
                placeholder="请输入验证码"
                value="{{verifyCode}}"
                bindinput="onCodeInput"
                maxlength="6"
              />
              <button 
                class="code-btn {{canSendCode ? '' : 'disabled'}}" 
                bindtap="sendVerifyCode"
                disabled="{{!canSendCode}}"
              >
                {{codeButtonText}}
              </button>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="modal-btn cancel-btn" bindtap="closePhoneModal">取消</button>
          <button 
            class="modal-btn confirm-btn" 
            bindtap="confirmBindPhone"
            disabled="{{!phoneNumber || !verifyCode || isBinding}}"
          >
            {{isBinding ? '绑定中...' : '确认绑定'}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">版本 {{version}}</text>
  </view>
</view>
