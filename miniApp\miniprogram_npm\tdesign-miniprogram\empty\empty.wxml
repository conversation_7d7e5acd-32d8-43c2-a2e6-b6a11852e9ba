<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="class {{prefix}}-class {{classPrefix}}"><view aria-hidden="true" class="{{classPrefix}}__thumb"><t-image wx:if="{{image}}" t-class="{{prefix}}-class-image" src="{{image}}" mode="aspectFit"/><template wx:elif="{{iconName || _.isNoEmptyObj(iconData)}}" is="icon" data="{{tClass: classPrefix + '__icon', name: iconName, ...iconData}}"/><slot wx:else name="image"/></view><view class="{{classPrefix}}__description {{prefix}}-class-description"><block wx:if="{{description}}">{{description}}</block><slot name="description"/></view><view class="{{classPrefix}}__actions {{prefix}}-class-actions"><slot name="action"/></view></view>