/* pages/maintenance/new-maintenance.wxss */
.maintenance-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
}

/* 项目信息卡片 */
.project-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.project-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.project-name {
  font-size: 32rpx;
  font-weight: 600;
  flex: 1;
  line-height: 1.4;
}

.project-detail-btn {
  font-size: 28rpx;
  opacity: 0.9;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.project-desc {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
}

.project-period {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 区域卡片 */
.section-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 交底状态 */
.briefing-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.briefing-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.briefing-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.status-icon {
  font-size: 20rpx;
}

/* 已完成交底 */
.briefing-completed {
  border: 2rpx solid #e8f5e8;
  border-radius: 12rpx;
  padding: 24rpx;
  background: #fafffe;
}

.briefing-info {
  display: flex;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.briefing-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
}

.briefing-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.briefing-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.briefing-time,
.briefing-person,
.briefing-location {
  font-size: 24rpx;
  color: #666;
}

.briefing-participants {
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.participants-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.participants-list {
  font-size: 24rpx;
  color: #333;
}

/* 未完成交底 */
.briefing-pending {
  text-align: center;
  padding: 40rpx 24rpx;
}

.pending-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 32rpx;
  padding: 16rpx 24rpx;
  background: #fff3e0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9800;
}

.notice-icon {
  font-size: 32rpx;
}

.notice-text {
  font-size: 28rpx;
  color: #e65100;
  line-height: 1.5;
}

.briefing-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 320rpx;
  height: 88rpx;
  background: #1976d2;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
  margin: 0 auto;
}

.briefing-btn:active {
  background: #1565c0;
  transform: translateY(2rpx);
}

/* 新增运维按钮 */
.new-maintenance-action {
  margin-bottom: 32rpx;
}

.new-maintenance-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.new-maintenance-btn:not(.disabled) {
  background: #4caf50;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

.new-maintenance-btn:not(.disabled):active {
  background: #43a047;
  transform: translateY(2rpx);
}

.new-maintenance-btn.disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

.record-count {
  font-size: 24rpx;
  color: #666;
}

/* 运维记录列表 */
.maintenance-records {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.record-item {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 24rpx;
  background: #fafafa;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.site-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.record-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.record-status.in-progress .status-dot {
  background: #ff9800;
}

.record-status.in-progress .status-text {
  color: #ff9800;
}

.record-status.completed .status-dot {
  background: #4caf50;
}

.record-status.completed .status-text {
  color: #4caf50;
}

.record-status.abnormal .status-dot {
  background: #f44336;
}

.record-status.abnormal .status-text {
  color: #f44336;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.record-time,
.record-duration {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.time-label,
.duration-label {
  color: #666;
  margin-right: 8rpx;
  min-width: 120rpx;
}

.time-value,
.duration-value {
  color: #333;
}

.record-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
}

.checkout-btn {
  background: #ff9800;
  color: #fff;
}

.detail-btn {
  background: #1976d2;
  color: #fff;
}

.handle-btn {
  background: #f44336;
  color: #fff;
}

/* 空状态 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
}

.empty-image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 快捷链接 */
.quick-links {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.link-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.link-item:last-child {
  border-bottom: none;
}

.link-item:active {
  background: #f5f5f5;
}

.link-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
}

.link-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.link-arrow {
  font-size: 24rpx;
  color: #999;
}
