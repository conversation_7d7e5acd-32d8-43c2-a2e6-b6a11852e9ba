module.exports = {
  getText: function (texts, val, defaultTexts) {
    if (!texts.length) {
      texts = defaultTexts;
    }
    var curVal = Math.floor(val - 1);
    return texts[curVal] || '未评分';
  },

  getIconName: function (defaultValue, value, index, icon) {
    var curVal = value >= 0 ? value : defaultValue;
    var name = ['star-filled', 'star-filled'];

    if (icon) {
      name = icon.constructor == 'Array' ? icon : [icon, icon];
    }

    return name[curVal >= index + 1 ? 0 : 1];
  },

  getIconClass: function (classPrefix, defaultValue, value, index, allowHalf, disabled, scaleIndex) {
    var curVal = value >= 0 ? value : defaultValue;
    var className = [];
    if (curVal >= index + 1) {
      className.push(classPrefix + '--selected');
      if (disabled) {
        className.push(classPrefix + '--disabled');
      }
      if (scaleIndex === index + 1) {
        className.push(classPrefix + '--current');
      }
    } else if (allowHalf && curVal - index > 0) {
      className.push(classPrefix + '--selected-half');
      if (scaleIndex === index + 1) {
        className.push(classPrefix + '--current');
      }
      if (disabled) {
        className.push(classPrefix + '--disabled-half');
      }
    } else {
      className.push(classPrefix + '--unselected');
    }
    return className.join(' ');
  },

  ceil: function (value) {
    return Math.ceil(value);
  },

  getColor: function (color) {
    if (color.constructor === 'Array' && color.length === 2) {
      return ';--td-rate-selected-color: ' + color[0] + '; --td-rate-unselected-color: ' + color[1];
    }

    if (typeof color === 'string') {
      return ';--td-rate-selected-color: ' + color;
    }

    return '';
  },

  regSize: function (val) {
    return val.indexOf('px') ? val : val + 'px';
  },
};
