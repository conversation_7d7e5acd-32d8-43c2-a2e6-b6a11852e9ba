<!--pages/site/new-site.wxml-->
<view class="new-site-container">
  <!-- 表单区域 -->
  <form bindsubmit="submitSite">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">基本信息</text>
      </view>
      
      <view class="form-group">
        <text class="form-label required">站点名称</text>
        <input 
          class="form-input" 
          placeholder="请输入站点名称（上限50字）"
          value="{{siteName}}"
          bindinput="onSiteNameInput"
          maxlength="50"
        />
        <text class="input-counter">{{siteName.length}}/50</text>
      </view>

      <view class="form-group">
        <text class="form-label required">站点编码</text>
        <input 
          class="form-input" 
          placeholder="请输入站点编码"
          value="{{siteCode}}"
          bindinput="onSiteCodeInput"
          maxlength="30"
        />
        <text class="input-counter">{{siteCode.length}}/30</text>
      </view>

      <view class="form-group">
        <text class="form-label required">站点类型</text>
        <picker 
          mode="selector" 
          range="{{siteTypeList}}" 
          range-key="name"
          value="{{siteTypeIndex}}" 
          bindchange="onSiteTypeChange"
        >
          <view class="form-picker {{siteTypeIndex === -1 ? 'placeholder' : ''}}">
            <text>{{siteTypeIndex === -1 ? '请选择站点类型' : siteTypeList[siteTypeIndex].name}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label required">站点地址</text>
        <textarea 
          class="form-textarea" 
          placeholder="请输入详细地址（上限100字）"
          value="{{siteAddress}}"
          bindinput="onSiteAddressInput"
          maxlength="100"
          auto-height
        />
        <text class="input-counter">{{siteAddress.length}}/100</text>
      </view>
    </view>

    <!-- 位置信息 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">位置信息</text>
        <text class="title-desc">请获取准确的经纬度坐标</text>
      </view>
      
      <view class="location-group">
        <button 
          class="location-btn {{locationStatus === 'success' ? 'success' : locationStatus === 'loading' ? 'loading' : ''}}"
          bindtap="getCurrentLocation"
          disabled="{{locationStatus === 'loading'}}"
        >
          <text class="btn-icon">
            {{locationStatus === 'success' ? '✓' : locationStatus === 'loading' ? '⟳' : '📍'}}
          </text>
          <text class="btn-text">
            {{locationStatus === 'success' ? '定位成功' : locationStatus === 'loading' ? '定位中...' : '获取当前位置'}}
          </text>
        </button>
        
        <view class="location-info" wx:if="{{currentLocation}}">
          <text class="location-address">{{currentLocation.address}}</text>
          <view class="location-coords">
            <text class="coord-item">经度：{{currentLocation.longitude}}</text>
            <text class="coord-item">纬度：{{currentLocation.latitude}}</text>
          </view>
        </view>
      </view>

      <view class="manual-input-section">
        <text class="manual-title">或手动输入坐标</text>
        
        <view class="coord-inputs">
          <view class="coord-input-group">
            <text class="coord-label">经度</text>
            <input 
              class="coord-input" 
              placeholder="如：119.296494"
              value="{{longitude}}"
              bindinput="onLongitudeInput"
              type="digit"
            />
          </view>
          <view class="coord-input-group">
            <text class="coord-label">纬度</text>
            <input 
              class="coord-input" 
              placeholder="如：26.075302"
              value="{{latitude}}"
              bindinput="onLatitudeInput"
              type="digit"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 站点照片 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">站点照片</text>
        <text class="title-desc">请拍摄站点外观照片（最多3张）</text>
      </view>
      
      <view class="photo-upload-area">
        <view class="photo-grid">
          <view class="photo-item" wx:for="{{sitePhotos}}" wx:key="index">
            <image 
              class="photo-image" 
              src="{{item}}" 
              mode="aspectFill"
              bindtap="previewPhoto"
              data-urls="{{sitePhotos}}"
              data-current="{{index}}"
            />
            <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
              <text class="delete-icon">×</text>
            </view>
          </view>
          
          <view 
            class="photo-add-btn" 
            wx:if="{{sitePhotos.length < 3}}"
            bindtap="takePhoto"
          >
            <text class="add-icon">📷</text>
            <text class="add-text">拍摄站点</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-text">备注信息</text>
      </view>
      
      <view class="form-group">
        <textarea 
          class="form-textarea" 
          placeholder="请输入备注信息（选填，上限200字）"
          value="{{siteRemark}}"
          bindinput="onSiteRemarkInput"
          maxlength="200"
          auto-height
        />
        <text class="input-counter">{{siteRemark.length}}/200</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn {{canSubmit ? '' : 'disabled'}}" 
        form-type="submit"
        disabled="{{!canSubmit || isSubmitting}}"
      >
        <text class="btn-text">{{isSubmitting ? '提交中...' : '提交'}}</text>
      </button>
    </view>
  </form>

  <!-- 提交遮罩 -->
  <view class="submit-overlay" wx:if="{{isSubmitting}}">
    <view class="submit-content">
      <view class="submit-spinner"></view>
      <text class="submit-text">正在创建站点...</text>
      <text class="submit-tip">请稍候，正在保存站点信息</text>
    </view>
  </view>
</view>
