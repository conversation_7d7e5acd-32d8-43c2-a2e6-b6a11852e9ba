package cn.iocoder.yudao.module.yunweibao.convert;

import cn.iocoder.yudao.module.yunweibao.controller.admin.vo.project.YwbProjectRespVO;
import cn.iocoder.yudao.module.yunweibao.controller.admin.vo.project.YwbProjectSaveReqVO;
import cn.iocoder.yudao.module.yunweibao.dal.dataobject.YwbProjectDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-14T11:26:58+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.2 (Oracle Corporation)"
)
public class YwbProjectConvertImpl implements YwbProjectConvert {

    @Override
    public YwbProjectDO convert(YwbProjectSaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        YwbProjectDO.YwbProjectDOBuilder ywbProjectDO = YwbProjectDO.builder();

        ywbProjectDO.id( bean.getId() );
        ywbProjectDO.projectName( bean.getProjectName() );
        ywbProjectDO.projectCode( bean.getProjectCode() );
        ywbProjectDO.projectDesc( bean.getProjectDesc() );
        ywbProjectDO.startDate( bean.getStartDate() );
        ywbProjectDO.endDate( bean.getEndDate() );
        ywbProjectDO.status( bean.getStatus() );
        ywbProjectDO.managerUserId( bean.getManagerUserId() );
        ywbProjectDO.deptId( bean.getDeptId() );

        return ywbProjectDO.build();
    }

    @Override
    public YwbProjectRespVO convert(YwbProjectDO bean) {
        if ( bean == null ) {
            return null;
        }

        YwbProjectRespVO.YwbProjectRespVOBuilder ywbProjectRespVO = YwbProjectRespVO.builder();

        ywbProjectRespVO.id( bean.getId() );
        ywbProjectRespVO.projectName( bean.getProjectName() );
        ywbProjectRespVO.projectCode( bean.getProjectCode() );
        ywbProjectRespVO.projectDesc( bean.getProjectDesc() );
        ywbProjectRespVO.startDate( bean.getStartDate() );
        ywbProjectRespVO.endDate( bean.getEndDate() );
        ywbProjectRespVO.status( bean.getStatus() );
        ywbProjectRespVO.managerUserId( bean.getManagerUserId() );
        ywbProjectRespVO.deptId( bean.getDeptId() );
        ywbProjectRespVO.createTime( bean.getCreateTime() );

        return ywbProjectRespVO.build();
    }

    @Override
    public List<YwbProjectRespVO> convertList(List<YwbProjectDO> list) {
        if ( list == null ) {
            return null;
        }

        List<YwbProjectRespVO> list1 = new ArrayList<YwbProjectRespVO>( list.size() );
        for ( YwbProjectDO ywbProjectDO : list ) {
            list1.add( convert( ywbProjectDO ) );
        }

        return list1;
    }
}
