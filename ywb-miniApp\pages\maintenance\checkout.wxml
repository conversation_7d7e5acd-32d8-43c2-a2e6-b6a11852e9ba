<!--pages/maintenance/checkout.wxml-->
<view class="checkout-container">
  <!-- 运维记录信息 -->
  <view class="record-info-section">
    <view class="section-title">
      <text class="title-text">运维记录</text>
    </view>
    
    <view class="record-card">
      <view class="record-header">
        <text class="record-title">{{recordInfo.siteName}}</text>
        <view class="record-status {{recordInfo.status === 1 ? 'ongoing' : ''}}">
          <text class="status-text">{{recordInfo.statusText}}</text>
        </view>
      </view>
      
      <view class="record-details">
        <view class="detail-row">
          <text class="detail-label">作业类型：</text>
          <text class="detail-value">{{recordInfo.workTypeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">上站时间：</text>
          <text class="detail-value">{{recordInfo.checkinTime}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">作业时长：</text>
          <text class="detail-value">{{workDuration}}</text>
        </view>
        <view class="detail-row" wx:if="{{recordInfo.safetyFactors}}">
          <text class="detail-label">安全要素：</text>
          <text class="detail-value">{{recordInfo.safetyFactorsText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 离站照片 -->
  <view class="photo-section">
    <view class="section-title">
      <text class="title-text">离站照片</text>
      <text class="required-mark">*</text>
    </view>
    
    <!-- 作业完成照片 -->
    <view class="photo-group">
      <view class="photo-header">
        <text class="photo-label">作业完成照片</text>
        <text class="photo-desc">请拍摄作业完成后的现场照片</text>
      </view>
      <view class="photo-grid">
        <view class="photo-item" wx:for="{{completionPhotos}}" wx:key="index">
          <image 
            class="photo-image" 
            src="{{item}}" 
            mode="aspectFill"
            bindtap="previewPhoto"
            data-urls="{{completionPhotos}}"
            data-current="{{index}}"
          />
          <view class="photo-delete" bindtap="deletePhoto" data-type="completion" data-index="{{index}}">
            <text class="delete-icon">×</text>
          </view>
        </view>
        <view 
          class="photo-add-btn" 
          wx:if="{{completionPhotos.length < 3}}"
          bindtap="takePhoto" 
          data-type="completion"
        >
          <text class="add-icon">📷</text>
          <text class="add-text">拍摄完成</text>
        </view>
      </view>
    </view>

    <!-- 工具整理照片 -->
    <view class="photo-group">
      <view class="photo-header">
        <text class="photo-label">工具整理照片</text>
        <text class="photo-desc">请拍摄工具整理收纳情况</text>
      </view>
      <view class="photo-grid">
        <view class="photo-item" wx:for="{{toolsPhotos}}" wx:key="index">
          <image 
            class="photo-image" 
            src="{{item}}" 
            mode="aspectFill"
            bindtap="previewPhoto"
            data-urls="{{toolsPhotos}}"
            data-current="{{index}}"
          />
          <view class="photo-delete" bindtap="deletePhoto" data-type="tools" data-index="{{index}}">
            <text class="delete-icon">×</text>
          </view>
        </view>
        <view 
          class="photo-add-btn" 
          wx:if="{{toolsPhotos.length < 2}}"
          bindtap="takePhoto" 
          data-type="tools"
        >
          <text class="add-icon">📷</text>
          <text class="add-text">拍摄工具</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 作业总结 -->
  <view class="summary-section">
    <view class="section-title">
      <text class="title-text">作业总结</text>
    </view>
    
    <view class="form-group">
      <text class="form-label">完成情况</text>
      <view class="completion-status">
        <view 
          class="status-option {{completionStatus === 'completed' ? 'selected' : ''}}"
          bindtap="selectCompletionStatus"
          data-status="completed"
        >
          <view class="option-icon">
            <text class="icon-text" wx:if="{{completionStatus === 'completed'}}">✓</text>
          </view>
          <text class="option-text">正常完成</text>
        </view>
        <view 
          class="status-option {{completionStatus === 'partial' ? 'selected' : ''}}"
          bindtap="selectCompletionStatus"
          data-status="partial"
        >
          <view class="option-icon">
            <text class="icon-text" wx:if="{{completionStatus === 'partial'}}">✓</text>
          </view>
          <text class="option-text">部分完成</text>
        </view>
        <view 
          class="status-option {{completionStatus === 'failed' ? 'selected' : ''}}"
          bindtap="selectCompletionStatus"
          data-status="failed"
        >
          <view class="option-icon">
            <text class="icon-text" wx:if="{{completionStatus === 'failed'}}">✓</text>
          </view>
          <text class="option-text">未能完成</text>
        </view>
      </view>
    </view>

    <view class="form-group">
      <text class="form-label">作业备注</text>
      <textarea 
        class="form-textarea" 
        placeholder="请输入作业总结和备注信息（上限300字）"
        value="{{workSummary}}"
        bindinput="onWorkSummaryInput"
        maxlength="300"
        auto-height
      />
      <text class="input-counter">{{workSummary.length}}/300</text>
    </view>
  </view>

  <!-- 异常情况 -->
  <view class="exception-section" wx:if="{{completionStatus !== 'completed'}}">
    <view class="section-title">
      <text class="title-text">异常情况</text>
    </view>
    
    <view class="form-group">
      <text class="form-label">异常类型</text>
      <view class="exception-types">
        <view 
          class="exception-tag {{exceptionTypes.includes('equipment') ? 'selected' : ''}}"
          bindtap="toggleExceptionType"
          data-type="equipment"
        >
          <text class="tag-text">设备故障</text>
        </view>
        <view 
          class="exception-tag {{exceptionTypes.includes('weather') ? 'selected' : ''}}"
          bindtap="toggleExceptionType"
          data-type="weather"
        >
          <text class="tag-text">天气原因</text>
        </view>
        <view 
          class="exception-tag {{exceptionTypes.includes('safety') ? 'selected' : ''}}"
          bindtap="toggleExceptionType"
          data-type="safety"
        >
          <text class="tag-text">安全隐患</text>
        </view>
        <view 
          class="exception-tag {{exceptionTypes.includes('material') ? 'selected' : ''}}"
          bindtap="toggleExceptionType"
          data-type="material"
        >
          <text class="tag-text">材料不足</text>
        </view>
        <view 
          class="exception-tag {{exceptionTypes.includes('other') ? 'selected' : ''}}"
          bindtap="toggleExceptionType"
          data-type="other"
        >
          <text class="tag-text">其他原因</text>
        </view>
      </view>
    </view>

    <view class="form-group">
      <text class="form-label">异常描述</text>
      <textarea 
        class="form-textarea" 
        placeholder="请详细描述异常情况（上限200字）"
        value="{{exceptionDescription}}"
        bindinput="onExceptionDescriptionInput"
        maxlength="200"
        auto-height
      />
      <text class="input-counter">{{exceptionDescription.length}}/200</text>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button 
      class="checkout-btn {{canCheckout ? '' : 'disabled'}}"
      bindtap="submitCheckout"
      disabled="{{!canCheckout || isSubmitting}}"
    >
      <text class="btn-text">{{isSubmitting ? '提交中...' : '完成离站'}}</text>
    </button>
  </view>

  <!-- 提交遮罩 -->
  <view class="submit-overlay" wx:if="{{isSubmitting}}">
    <view class="submit-content">
      <view class="submit-spinner"></view>
      <text class="submit-text">正在提交离站信息...</text>
      <text class="submit-tip">请稍候，正在保存作业记录</text>
    </view>
  </view>
</view>
