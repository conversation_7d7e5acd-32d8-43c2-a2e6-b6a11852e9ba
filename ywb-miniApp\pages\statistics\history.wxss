/* pages/statistics/history.wxss */
.history-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 筛选区域 */
.filter-section {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.date-picker {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fafafa;
}

.picker-label {
  font-size: 22rpx;
  color: #666;
}

.picker-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.date-separator {
  font-size: 24rpx;
  color: #666;
  padding: 0 8rpx;
}

.status-picker {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fafafa;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
}

.search-btn {
  flex: 1;
  background: #1976d2;
  color: #fff;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  height: 72rpx;
  font-weight: 500;
}

.search-btn:active {
  background: #1565c0;
}

/* 统计信息 */
.summary-section {
  display: flex;
  background: #fff;
  margin: 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.summary-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.summary-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #1976d2;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
}

/* 记录区域 */
.records-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 300rpx);
  margin: 0 32rpx 32rpx;
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  gap: 24rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.empty-icon {
  font-size: 120rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 记录滚动列表 */
.records-scroll {
  flex: 1;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.records-list {
  padding: 16rpx;
}

.record-item {
  padding: 24rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.record-item:last-child {
  margin-bottom: 0;
}

.record-item:active {
  background: #f0f0f0;
  transform: translateY(1rpx);
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.record-site {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.record-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.record-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.record-status.ongoing {
  background: #fff3e0;
  color: #ff9800;
}

.record-status.exception {
  background: #ffebee;
  color: #f44336;
}

.record-content {
  margin-bottom: 16rpx;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.info-item {
  font-size: 24rpx;
  color: #666;
}

.info-separator {
  font-size: 20rpx;
  color: #ccc;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.record-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid #e0e0e0;
  padding-top: 12rpx;
}

.project-name {
  font-size: 22rpx;
  color: #999;
  flex: 1;
}

.record-code {
  font-size: 20rpx;
  color: #ccc;
  font-family: 'Courier New', monospace;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
}

.load-more-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #f0f0f0;
  border-top: 2rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-text {
  font-size: 24rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 32rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .filter-row {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .date-picker,
  .status-picker,
  .search-btn {
    flex: none;
    width: 100%;
  }
  
  .summary-section {
    flex-direction: column;
    gap: 24rpx;
  }
  
  .summary-item {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .record-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }
  
  .info-separator {
    display: none;
  }
}
