// pages/maintenance/safety-briefing.js
const app = getApp()
const { safetyAPI, photoAPI } = require('../../utils/api')
const { formatTime, chooseImage, previewImage, showSuccess, showError } = require('../../utils/util')

Page({
  data: {
    projectId: '',
    userInfo: {},
    briefingDate: '',
    
    // 表单数据
    areaList: [],
    areaIndex: -1,
    briefingName: '',
    briefingLocation: '',
    briefingRemark: '',
    
    // 照片数据
    briefingRecordPhoto: '',
    safetyGearPhoto: '',
    
    // 状态
    isSubmitting: false,
    canSubmit: false
  },

  onLoad(options) {
    console.log('安全交底页面加载', options)
    
    const { projectId } = options
    
    if (!projectId) {
      wx.showToast({
        title: '项目参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }
    
    this.setData({
      projectId,
      userInfo: app.globalData.userInfo || {},
      briefingDate: formatTime(new Date(), 'YYYY-MM-DD')
    })
    
    this.initPage()
  },

  // 初始化页面
  initPage() {
    this.loadAreaList()
    this.checkCanSubmit()
  },

  // 加载管理区域列表
  loadAreaList() {
    // TODO: 调用API获取管理区域列表
    // 暂时使用模拟数据
    const mockAreas = [
      { id: 1, name: '连江区域' },
      { id: 2, name: '福清区域' },
      { id: 3, name: '长乐区域' },
      { id: 4, name: '闽侯区域' },
      { id: 5, name: '罗源区域' }
    ]
    
    this.setData({ areaList: mockAreas })
  },

  // 管理区域选择
  onAreaChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({ areaIndex: index })
    this.checkCanSubmit()
  },

  // 交底名称输入
  onBriefingNameInput(e) {
    this.setData({ briefingName: e.detail.value })
    this.checkCanSubmit()
  },

  // 交底地点输入
  onBriefingLocationInput(e) {
    this.setData({ briefingLocation: e.detail.value })
    this.checkCanSubmit()
  },

  // 备注输入
  onBriefingRemarkInput(e) {
    this.setData({ briefingRemark: e.detail.value })
  },

  // 拍照
  takePhoto(e) {
    const type = e.currentTarget.dataset.type
    
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
        
        chooseImage(1, ['compressed'], sourceType)
          .then(tempFilePaths => {
            this.uploadPhoto(tempFilePaths[0], type)
          })
          .catch(error => {
            console.error('选择图片失败:', error)
          })
      }
    })
  },

  // 上传照片
  uploadPhoto(filePath, type) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })
    
    // 根据类型确定业务类型
    const businessType = 'briefing'
    const businessId = this.data.projectId // 临时使用项目ID，实际应该是交底记录ID
    
    photoAPI.uploadPhoto(filePath, businessType, businessId)
      .then(res => {
        wx.hideLoading()
        
        const photoUrl = res.data.photoUrl
        
        if (type === 'record') {
          this.setData({ briefingRecordPhoto: photoUrl })
        } else if (type === 'gear') {
          this.setData({ safetyGearPhoto: photoUrl })
        }
        
        this.checkCanSubmit()
        
        showSuccess('照片上传成功')
      })
      .catch(error => {
        wx.hideLoading()
        console.error('照片上传失败:', error)
        showError('照片上传失败，请重试')
      })
  },

  // 预览照片
  previewPhoto(e) {
    const url = e.currentTarget.dataset.url
    previewImage([url])
  },

  // 删除照片
  deletePhoto(e) {
    const type = e.currentTarget.dataset.type
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？',
      success: (res) => {
        if (res.confirm) {
          if (type === 'record') {
            this.setData({ briefingRecordPhoto: '' })
          } else if (type === 'gear') {
            this.setData({ safetyGearPhoto: '' })
          }
          
          this.checkCanSubmit()
        }
      }
    })
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const {
      areaIndex,
      briefingName,
      briefingLocation,
      briefingRecordPhoto,
      safetyGearPhoto
    } = this.data
    
    const canSubmit = areaIndex !== -1 &&
                     briefingName.trim() !== '' &&
                     briefingLocation.trim() !== '' &&
                     briefingRecordPhoto !== '' &&
                     safetyGearPhoto !== ''
    
    this.setData({ canSubmit })
  },

  // 提交交底
  submitBriefing(e) {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return
    }
    
    const {
      projectId,
      areaList,
      areaIndex,
      briefingName,
      briefingLocation,
      briefingRemark,
      briefingRecordPhoto,
      safetyGearPhoto
    } = this.data
    
    // 验证必填项
    if (areaIndex === -1) {
      showError('请选择管理区域')
      return
    }
    
    if (!briefingName.trim()) {
      showError('请输入交底名称')
      return
    }
    
    if (!briefingLocation.trim()) {
      showError('请输入交底地点')
      return
    }
    
    if (!briefingRecordPhoto) {
      showError('请拍摄安全交底记录单')
      return
    }
    
    if (!safetyGearPhoto) {
      showError('请拍摄防护用品照片')
      return
    }
    
    this.setData({ isSubmitting: true })
    
    // 构建提交数据
    const briefingData = {
      projectId,
      briefingDate: this.data.briefingDate,
      briefingName: briefingName.trim(),
      briefingLocation: briefingLocation.trim(),
      briefingRemark: briefingRemark.trim(),
      briefingPerson: this.data.userInfo.nickname || this.data.userInfo.username,
      managementArea: areaList[areaIndex].name,
      recordPhotoUrl: briefingRecordPhoto,
      safetyGearPhotoUrl: safetyGearPhoto,
      participants: [
        {
          userId: this.data.userInfo.id,
          name: this.data.userInfo.nickname || this.data.userInfo.username
        }
      ]
    }
    
    // 调用API提交
    safetyAPI.createSafetyBriefing(briefingData)
      .then(res => {
        showSuccess('安全交底提交成功')
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      })
      .catch(error => {
        console.error('提交安全交底失败:', error)
        showError(error.message || '提交失败，请重试')
      })
      .finally(() => {
        this.setData({ isSubmitting: false })
      })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '安全交底 - 运维宝',
      path: `/pages/maintenance/safety-briefing?projectId=${this.data.projectId}`,
      imageUrl: '/images/share-briefing.png'
    }
  }
})
