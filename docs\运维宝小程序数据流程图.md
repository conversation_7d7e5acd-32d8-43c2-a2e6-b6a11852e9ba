# 运维宝小程序数据流程图

## 1. 总体数据架构

```
┌─────────────────────────────────────────────────────────────┐
│                    运维宝数据架构                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │  前端小程序      │    │   后端管理系统   │               │
│  │  (微信小程序)    │    │   (SpringBoot)  │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                      │
│           ▼                       ▼                      │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │   API接口层     │◀──▶│   业务逻辑层    │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                      │
│  ┌─────────────┐   ┌─────────────┐   │                      │
│  │ 微信登录API  │   │ 业务功能API  │   │                      │
│  └─────────────┘   └─────────────┘   │                      │
│           │                       │                      │
│           ▼                       ▼                      │
│  ┌─────────────────────────────────────────────────┐     │
│  │                  数据持久层                        │     │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌──────┐ │     │
│  │  │项目表    │  │站点表    │  │运维表    │  │用户表│ │     │
│  │  │ywb_     │  │ywb_     │  │ywb_     │  │system│ │     │
│  │  └─────────┘  └─────────┘  └─────────┘  └──────┘ │     │
│  │  ┌─────────┐  ┌─────────┐  ┌─────────┐              │     │
│  │  │社交用户  │  │OAuth2   │  │字典表    │              │     │
│  │  │social_  │  │oauth2_  │  │dict_    │              │     │
│  │  └─────────┘  └─────────┘  └─────────┘              │     │
│  └─────────────────────────────────────────────────┘     │
│           │                       │                      │
│           ▼                       ▼                      │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │   MinIO存储    │    │   MySQL数据库   │               │
│  │  (照片/视频)    │    │   (业务数据)    │               │
│  └─────────────────┘    └─────────────────┘               │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 2. 菜单页面与数据表关系映射

### 2.1 运维管理模块

```
┌─────────────────────────────────────────────────────────────┐
│                  运维管理数据映射                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  项目管理    │◀──────▶│ ywb_project (项目表)           │ │
│  └─────────────┘        │ - id, project_name, project_code│ │
│                         │ - start_date, end_date, status   │ │
│                         │ - manager_user_id, dept_id      │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  新增运维    │◀──────▶│ ywb_maintenance_record (运维记录)│ │
│  └─────────────┘        │ - user_id, project_id, site_id   │ │
│                         │ - work_date, work_content, status│ │
│                         │ - checkin_time, checkout_time    │ │
│                         │ - work_duration, is_briefed     │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  安全交底    │◀──────▶│ ywb_safety_briefing (安全交底)  │ │
│  └─────────────┘        │ - project_id, briefing_date     │ │
│                         │ - briefing_content, video_url    │ │
│                         │ - briefing_person, participants  │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  上站打卡    │◀──────▶│ ywb_maintenance_record (更新)    │ │
│  └─────────────┘        │ - checkin_time, checkin_location│ │
│                         │ - checkin_address                │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  离站打卡    │◀──────▶│ ywb_maintenance_record (更新)    │ │
│  └─────────────┘        │ - checkout_time, checkout_location│ │
│                         │ - checkout_address, work_duration│ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  站点选择    │◀──────▶│ ywb_site (站点表)              │ │
│  └─────────────┘        │ - id, site_name, site_code     │ │
│                         │ - site_address, longitude, latitude│ │
│                         │ - site_type, project_id, status │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  新增站点    │◀──────▶│ ywb_site (新增)                │ │
│  └─────────────┘        │ - is_new_site = true (待审核)   │ │
│                         └─────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 照片与AI分析模块

```
┌─────────────────────────────────────────────────────────────┐
│                照片与AI分析数据映射                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  上站拍照    │◀──────▶│ ywb_photo (照片表)              │ │
│  └─────────────┘        │ - business_type = 'checkin'    │ │
│                         │ - business_id (关联运维记录)    │ │
│                         │ - photo_url, upload_user_id     │ │
│                         │ - longitude, latitude, address │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  离站拍照    │◀──────▶│ ywb_photo (照片表)              │ │
│  └─────────────┘        │ - business_type = 'checkout'   │ │
│                         │ - business_id (关联运维记录)    │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  交底拍照    │◀──────▶│ ywb_photo (照片表)              │ │
│  └─────────────┘        │ - business_type = 'briefing'   │ │
│                         │ - business_id (关联交底记录)    │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  站点拍照    │◀──────▶│ ywb_photo (照片表)              │ │
│  └─────────────┘        │ - business_type = 'site'       │ │
│                         │ - business_id (关联站点记录)    │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  AI人脸识别  │◀──────▶│ ywb_ai_analysis (AI分析表)      │ │
│  └─────────────┘        │ - analysis_type = 'face'        │ │
│                         │ - photo_id, analysis_result      │ │
│                         │ - confidence_score, is_passed    │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  AI证件识别  │◀──────▶│ ywb_ai_analysis (AI分析表)      │ │
│  └─────────────┘        │ - analysis_type = 'id_card'     │ │
│                         │ - analysis_result (证件信息)    │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  AI防护检测  │◀──────▶│ ywb_ai_analysis (AI分析表)      │ │
│  └─────────────┘        │ - analysis_type = 'safety_gear' │ │
│                         │ - analysis_result (防护用品)    │ │
│                         └─────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 统计分析模块

```
┌─────────────────────────────────────────────────────────────┐
│                 统计分析数据映射                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  历史运维    │◀──────▶│ ywb_maintenance_record (聚合查询)│ │
│  └─────────────┘        │ - GROUP BY work_date, project_id │ │
│                         │ - COUNT(site_id), 计算work_duration│ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  我的统计    │◀──────▶│ ywb_maintenance_record +        │ │
│  └─────────────┘        │ system_users (用户表)           │ │
│                         │ - 按月统计用户运维数据            │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  按地市统计  │◀──────▶│ ywb_site + ywb_maintenance_record│ │
│  └─────────────┘        │ - 按站点地址区域分组统计         │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  按项目统计  │◀──────▶│ ywb_project + ywb_maintenance_   │ │
│  └─────────────┘        │ record (项目维度统计)           │ │
│                         └─────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.4 系统管理模块

```
┌─────────────────────────────────────────────────────────────┐
│                 系统管理数据映射                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  用户登录    │◀──────▶│ system_users (用户表)           │ │
│  └─────────────┘        │ - username, mobile, status     │ │
│                         │ - login_ip, login_date          │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  个人信息    │◀──────▶│ system_users (用户信息)         │ │
│  └─────────────┘        │ - nickname, avatar, email      │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  权限控制    │◀──────▶│ system_role + system_user_role  │ │
│  └─────────────┘        │ - 角色权限关联表                │ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  部门管理    │◀──────▶│ system_dept (部门表)           │ │
│  └─────────────┘        │ - name, parent_id, leader_user_id│ │
│                         └─────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│  ┌─────────────┐        ┌─────────────────────────────────┐ │
│  │  字典数据    │◀──────▶│ system_dict_type +              │ │
│  └─────────────┘        │ system_dict_data (字典表)       │ │
│                         │ - 项目状态、站点类型、业务类型等  │ │
│                         └─────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 3. 核心数据流程

### 3.1 完整运维业务数据流程

```
┌─────────────────────────────────────────────────────────────┐
│                完整运维业务数据流程                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  用户登录                                                  │
│     │                                                      │
│     ▼                                                      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  system_    │    │  ywb_       │    │  ywb_       │     │
│  │  users      │    │  project    │    │  safety_     │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│     │                      │                   │          │
│     ▼                      ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  项目选择    │───▶│  安全交底    │───▶│  上站打卡    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                 │                   │          │
│                                 ▼                   ▼          │
│                        ┌─────────────┐    ┌─────────────┐     │
│                        │  ywb_       │    │  ywb_       │     │
│                        │  photo      │    │  ai_analysis │     │
│                        │  (交底照片)  │    │  (AI验证)    │     │
│                        └─────────────┘    └─────────────┘     │
│                                 │                   │          │
│                                 ▼                   ▼          │
│                        ┌─────────────┐    ┌─────────────┐     │
│                        │  视频观看    │    │  离站打卡    │     │
│                        │  验证        │    └─────────────┘     │
│                        └─────────────┘         │          │
│                                                   ▼          │
│                                          ┌─────────────┐     │
│                                          │  ywb_       │     │
│                                          │  photo      │     │
│                                          │  (离站照片)  │     │
│                                          └─────────────┘     │
│                                                             │
│  数据流转：用户→项目→交底→上站(AI验证)→离站→完成              │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 照片AI分析数据流程

```
┌─────────────────────────────────────────────────────────────┐
│                照片AI分析数据流程                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  用户拍照                                                  │
│     │                                                      │
│     ▼                                                      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  前端上传    │───▶│  MinIO      │    │  ywb_       │     │
│  │  照片        │    │  存储       │    │  photo      │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                 │                   │          │
│                                 ▼                   ▼          │
│                        ┌─────────────┐    ┌─────────────┐     │
│                        │  AI服务     │    │  ywb_       │     │
│                        │  分析       │───▶│  ai_analysis│     │
│                        │             │    │  记录       │     │
│                        └─────────────┘    └─────────────┘     │
│                                 │                   │          │
│                                 ▼                   ▼          │
│                        ┌─────────────┐    ┌─────────────┐     │
│                        │  分析结果    │    │  业务日志    │     │
│                        │  返回前端    │    │  记录       │     │
│                        └─────────────┘    └─────────────┘     │
│                                                             │
│  分析类型：人脸识别、证件识别、防护用品检测、场景识别        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 4. 数据表关系总结

### 4.1 主要业务表关系

```
ywb_project (项目表)
    ├── ywb_site (站点表) - project_id
    ├── ywb_safety_briefing (安全交底表) - project_id
    └── ywb_maintenance_record (运维记录表) - project_id

ywb_site (站点表)
    └── ywb_maintenance_record (运维记录表) - site_id

ywb_safety_briefing (安全交底表)
    └── ywb_maintenance_record (运维记录表) - briefing_id

ywb_maintenance_record (运维记录表)
    ├── ywb_photo (照片表) - business_id + business_type
    └── ywb_business_log (业务日志表) - business_id + business_type

ywb_photo (照片表)
    └── ywb_ai_analysis (AI分析表) - photo_id

system_users (用户表)
    ├── ywb_project (项目表) - manager_user_id
    ├── ywb_maintenance_record (运维记录表) - user_id
    ├── ywb_photo (照片表) - upload_user_id
    └── ywb_business_log (业务日志表) - user_id
```

### 4.2 字典数据关联

```
system_dict_type (字典类型表)
    └── system_dict_data (字典数据表)
        ├── ywb_project.status - project_status (项目状态)
        ├── ywb_site.status - site_status (站点状态)
        ├── ywb_site.site_type - site_type (站点类型)
        ├── ywb_maintenance_record.status - maintenance_status (运维状态)
        ├── ywb_photo.business_type - photo_business_type (照片业务类型)
        └── ywb_ai_analysis.analysis_type - ai_analysis_type (AI分析类型)
```

## 5. 数据操作频率分析

### 5.1 高频操作表
- `ywb_maintenance_record`: 运维记录，每日多次读写
- `ywb_photo`: 照片上传，频繁插入和查询
- `ywb_ai_analysis`: AI分析结果，实时写入
- `ywb_business_log`: 业务日志，大量插入

### 5.2 中频操作表
- `ywb_site`: 站点查询和新增
- `ywb_safety_briefing`: 每日交底记录
- `system_users`: 用户登录信息更新
- `system_social_user`: 社交用户信息查询和更新
- `system_oauth2_access_token`: 登录令牌创建和验证

### 5.3 低频操作表
- `ywb_project`: 项目配置，较少变更
- `system_dict_type/data`: 字典数据，基本不变
- `system_dept/role`: 组织架构，变更较少
- `system_social_client`: 社交客户端配置，基本不变
- `system_social_user_bind`: 社交用户绑定，创建后较少变更

### 5.4 存储特点
- **文本数据**: MySQL存储结构化业务数据
- **大文件**: MinIO存储照片、视频等媒体文件
- **分析结果**: JSON格式存储AI分析结果，便于扩展
- **地理位置**: decimal类型存储经纬度，支持地理位置查询

## 6. 微信登录数据流程

### 6.1 微信登录数据架构

```
┌─────────────────────────────────────────────────────────────┐
│                   微信登录数据流程                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  微信小程序    │    │   后端API    │    │  微信服务器    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ wx.login()   │    │ 登录接口     │    │ code换取      │     │
│  │ 获取code     │───▶│ 处理        │◀───│ openid+      │     │
│  └─────────────┘    │             │    │ session_key  │     │
│                       └─────────────┘    └─────────────┘     │
│                                │                              │
│                                ▼                              │
│  ┌─────────────────────────────────────────────────┐         │
│  │                用户数据验证层                      │         │
│  │ ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │         │
│  │ │查询社交用户  │  │查询系统用户  │  │OAuth2    │ │         │
│  │ │social_user  │  │system_users │  │token     │ │         │
│  │ └─────────────┘  └─────────────┘  └──────────┘ │         │
│  └─────────────────────────────────────────────────┘         │
│                                │                              │
│                                ▼                              │
│  ┌─────────────────────────────────────────────────┐         │
│  │                用户绑定处理                      │         │
│  │ ┌─────────────┐  ┌─────────────┐               │         │
│  │ │存在绑定     │  │需要绑定     │               │         │
│  │ │直接登录     │  │手机号验证   │               │         │
│  │ └─────────────┘  └─────────────┘               │         │
│  └─────────────────────────────────────────────────┘         │
│                                │                              │
│                                ▼                              │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  JWT Token   │    │  用户信息    │    │  业务功能    │     │
│  │  返回前端     │    │  同步更新    │    │  正常使用    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 微信登录表操作流程

```
用户发起微信登录
    ↓
wx.login() 获取 code
    ↓
调用后端登录API /api/auth/wechat-login
    ↓
后端使用 code 向微信服务器换取 openid + session_key
    ↓
查询 system_social_user 表是否存在该 openid
    ↓
┌─────────────────────────────────────────────────┐
│            存在社交用户记录？                    │
├─────────────────────────────────────────────────┤
│                 YES                            │ NO
│                 ↓                               ↓
│         查询 system_social_user_bind         创建 system_social_user 记录
│         获取绑定的 user_id                   ↓
│                 ↓                               需要手机号绑定
│         查询 system_users 获取用户信息        ↓
│                 ↓                               绑定到现有系统用户
│         生成 JWT Token                       ↓
│                 ↓                               生成 JWT Token
│         返回登录成功                         ↓
│                                                 返回登录成功
└─────────────────────────────────────────────────┘
    ↓
记录登录日志到 system_oauth2_access_token
    ↓
更新用户最后登录信息到 system_users
```

### 6.3 微信登录相关表关系

```
system_users (系统用户表)
    ├── system_social_user_bind (社交用户绑定表) - user_id
    │     └── system_social_user (社交用户表) - social_user_id
    │           ├── type: 34 (微信小程序)
    │           ├── openid: 微信用户唯一标识
    │           ├── nickname: 微信昵称
    │           └── avatar: 微信头像
    │
    └── system_oauth2_access_token (访问令牌表) - user_id
          ├── access_token: JWT访问令牌
          ├── refresh_token: 刷新令牌
          └── expires_time: 过期时间

system_social_client (社交客户端配置表)
    ├── social_type: 34 (微信小程序)
    ├── client_id: 微信小程序AppID
    └── client_secret: 微信小程序AppSecret
```

### 6.4 微信登录数据验证要点

1. **openid 唯一性验证**: 确保同一微信用户只能绑定一个系统用户
2. **手机号绑定验证**: 新用户需要提供有效手机号进行绑定
3. **用户状态验证**: 检查系统用户是否启用 (status=0)
4. **令牌有效性**: 定期检查和清理过期的访问令牌
5. **登录频率限制**: 防止恶意频繁登录请求