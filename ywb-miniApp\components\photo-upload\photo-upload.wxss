/* components/photo-upload/photo-upload.wxss */
.photo-upload-container {
  width: 100%;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.photo-add-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.photo-add-btn:active {
  background: #f0f0f0;
  border-color: #1976d2;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.add-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.upload-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .photo-item,
  .photo-add-btn {
    width: 160rpx;
    height: 160rpx;
  }
  
  .add-icon {
    font-size: 40rpx;
  }
  
  .add-text {
    font-size: 22rpx;
  }
}
