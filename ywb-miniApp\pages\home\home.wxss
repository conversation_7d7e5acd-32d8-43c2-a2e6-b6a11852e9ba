/* pages/home/<USER>/
.home-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 头部样式 */
.home-header {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-actions {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 内容区域 */
.content-section {
  padding: 32rpx;
  flex: 1;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 项目列表 */
.project-list {
  max-height: 800rpx;
}

.project-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.project-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.12);
}

.project-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.project-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.project-status {
  margin-left: 16rpx;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-tag.active {
  background: #e8f5e8;
  color: #4caf50;
}

.status-tag.completed {
  background: #e3f2fd;
  color: #1976d2;
}

.status-tag.inactive {
  background: #f5f5f5;
  color: #999;
}

.project-info {
  margin-bottom: 24rpx;
}

.project-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.project-date {
  font-size: 24rpx;
  color: #999;
}

.project-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
}

.briefing-count {
  color: #1976d2;
}

.maintenance-count {
  color: #4caf50;
}

.project-action {
  margin-left: 24rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #1976d2;
  color: #fff;
  border-radius: 24rpx;
  font-size: 24rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3);
}

.action-btn:active {
  background: #1565c0;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  padding: 32rpx;
  background: #fff;
  margin: 0 32rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  background: #f5f5f5;
  transform: scale(0.95);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 16rpx;
}

.safety-icon {
  background: #fff3e0;
}

.checkin-icon {
  background: #e8f5e8;
}

.site-icon {
  background: #e3f2fd;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 今日概览 */
.today-overview {
  margin: 0 32rpx 32rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.overview-date {
  font-size: 24rpx;
  color: #666;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 8rpx;
}

.overview-label {
  font-size: 24rpx;
  color: #666;
}

/* 通知公告 */
.notice-section {
  margin: 0 32rpx 32rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.notice-header {
  margin-bottom: 24rpx;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.notice-list {
  white-space: nowrap;
}

.notice-item {
  display: inline-block;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  margin-right: 16rpx;
  min-width: 400rpx;
  vertical-align: top;
  white-space: normal;
}

.notice-content {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.notice-time {
  font-size: 24rpx;
  color: #999;
}

/* 下拉刷新 */
.project-list {
  height: 100%;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .project-stats {
    flex-wrap: wrap;
    gap: 16rpx;
  }
  
  .stat-item {
    flex: 1;
    min-width: 120rpx;
  }
  
  .project-action {
    width: 100%;
    margin-left: 0;
    margin-top: 16rpx;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
