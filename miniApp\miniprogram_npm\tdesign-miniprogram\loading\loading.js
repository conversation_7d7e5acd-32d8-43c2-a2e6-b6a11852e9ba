import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-loading`;let Loading=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-text`,`${prefix}-class-indicator`],this.data={prefix:prefix,classPrefix:name,show:!0},this.options={multipleSlots:!0},this.properties=Object.assign({},props),this.timer=null,this.observers={loading(e){const{delay:t}=this.properties;this.timer&&clearTimeout(this.timer),e&&t?this.timer=setTimeout((()=>{this.setData({show:e}),this.timer=null}),t):this.setData({show:e})}},this.lifetimes={detached(){clearTimeout(this.timer)}}}refreshPage(){this.triggerEvent("reload")}};Loading=__decorate([wxComponent()],Loading);export default Loading;