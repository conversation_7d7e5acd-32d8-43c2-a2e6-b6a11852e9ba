-- =============================================
-- 运维宝小程序数据库建表语句
-- 基于ruoyi-vue-pro框架
-- 创建时间: 2025-01-13
-- =============================================

-- =============================================
-- 1. 项目管理表
-- =============================================
DROP TABLE IF EXISTS `ywb_project`;
CREATE TABLE `ywb_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) NOT NULL COMMENT '项目编码',
  `project_desc` text COMMENT '项目描述',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '项目状态(0:停用 1:启用 2:已完成)',
  `manager_user_id` bigint COMMENT '项目负责人ID',
  `dept_id` bigint COMMENT '所属部门ID',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_manager_user_id` (`manager_user_id`),
  KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运维项目表';

-- =============================================
-- 2. 站点管理表
-- =============================================
DROP TABLE IF EXISTS `ywb_site`;
CREATE TABLE `ywb_site` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `site_name` varchar(100) NOT NULL COMMENT '站点名称',
  `site_code` varchar(50) NOT NULL COMMENT '站点编码',
  `site_address` varchar(200) NOT NULL COMMENT '站点地址',
  `longitude` decimal(10,7) COMMENT '经度',
  `latitude` decimal(10,7) COMMENT '纬度',
  `site_type` varchar(20) NOT NULL COMMENT '站点类型(机房/基站/光缆等)',
  `contact_person` varchar(50) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `project_id` bigint NOT NULL COMMENT '所属项目ID',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '站点状态(0:待审核 1:正常 2:停用)',
  `is_new_site` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否新采集站点',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_code` (`site_code`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_location` (`longitude`, `latitude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运维站点表';

-- =============================================
-- 3. 安全交底表
-- =============================================
DROP TABLE IF EXISTS `ywb_safety_briefing`;
CREATE TABLE `ywb_safety_briefing` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '交底ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `briefing_date` date NOT NULL COMMENT '交底日期',
  `briefing_content` text COMMENT '交底内容',
  `video_url` varchar(500) COMMENT '交底视频URL(MinIO)',
  `briefing_person` varchar(50) NOT NULL COMMENT '交底人',
  `participants` text COMMENT '参与人员(JSON格式存储用户ID数组)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(1:有效 0:无效)',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_project_date` (`project_id`, `briefing_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安全交底表';

-- =============================================
-- 4. 运维记录表
-- =============================================
DROP TABLE IF EXISTS `ywb_maintenance_record`;
CREATE TABLE `ywb_maintenance_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '运维人员ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `site_id` bigint NOT NULL COMMENT '站点ID',
  `work_date` date NOT NULL COMMENT '作业日期',
  `checkin_time` datetime COMMENT '上站打卡时间',
  `checkin_longitude` decimal(10,7) COMMENT '上站打卡经度',
  `checkin_latitude` decimal(10,7) COMMENT '上站打卡纬度',
  `checkin_address` varchar(200) COMMENT '上站打卡地址',
  `checkout_time` datetime COMMENT '离站打卡时间',
  `checkout_longitude` decimal(10,7) COMMENT '离站打卡经度',
  `checkout_latitude` decimal(10,7) COMMENT '离站打卡纬度',
  `checkout_address` varchar(200) COMMENT '离站打卡地址',
  `work_duration` int COMMENT '作业时长(分钟)',
  `work_content` text COMMENT '作业内容',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '记录状态(1:进行中 2:已完成 3:异常)',
  `is_briefed` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已交底',
  `briefing_id` bigint COMMENT '关联交底ID',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`, `work_date`),
  KEY `idx_project_site` (`project_id`, `site_id`),
  KEY `idx_briefing_id` (`briefing_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运维记录表';

-- =============================================
-- 5. 照片管理表
-- =============================================
DROP TABLE IF EXISTS `ywb_photo`;
CREATE TABLE `ywb_photo` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型(checkin:上站 checkout:离站 briefing:交底 site:站点)',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `photo_url` varchar(500) NOT NULL COMMENT '照片URL(MinIO存储)',
  `photo_name` varchar(100) COMMENT '照片名称',
  `photo_size` bigint COMMENT '照片大小(字节)',
  `photo_type` varchar(10) COMMENT '照片类型(jpg/png等)',
  `upload_user_id` bigint NOT NULL COMMENT '上传用户ID',
  `longitude` decimal(10,7) COMMENT '拍照经度',
  `latitude` decimal(10,7) COMMENT '拍照纬度',
  `address` varchar(200) COMMENT '拍照地址',
  `is_ai_analyzed` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已AI分析',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_upload_user` (`upload_user_id`),
  KEY `idx_ai_analyzed` (`is_ai_analyzed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='照片管理表';

-- =============================================
-- 6. AI分析记录表
-- =============================================
DROP TABLE IF EXISTS `ywb_ai_analysis`;
CREATE TABLE `ywb_ai_analysis` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分析ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `analysis_type` varchar(20) NOT NULL COMMENT '分析类型(face:人脸识别 id_card:身份证 safety_gear:防护用品 scene:场景识别)',
  `analysis_result` json COMMENT 'AI分析结果(JSON格式)',
  `confidence_score` decimal(5,4) COMMENT '置信度分数(0-1)',
  `is_passed` bit(1) COMMENT '是否通过检测',
  `error_message` varchar(500) COMMENT '错误信息',
  `analysis_time` datetime NOT NULL COMMENT '分析时间',
  `ai_model_version` varchar(50) COMMENT 'AI模型版本',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_analysis_type` (`analysis_type`),
  KEY `idx_analysis_time` (`analysis_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI分析记录表';

-- =============================================
-- 7. 统一业务日志表
-- =============================================
DROP TABLE IF EXISTS `ywb_business_log`;
CREATE TABLE `ywb_business_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NOT NULL COMMENT '操作用户ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型(project:项目 site:站点 maintenance:运维 briefing:交底)',
  `business_id` bigint COMMENT '业务ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型(create:创建 update:更新 delete:删除 checkin:上站 checkout:离站)',
  `operation_desc` varchar(200) NOT NULL COMMENT '操作描述',
  `request_params` json COMMENT '请求参数(JSON格式)',
  `response_result` json COMMENT '响应结果(JSON格式)',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `longitude` decimal(10,7) COMMENT '操作位置经度',
  `latitude` decimal(10,7) COMMENT '操作位置纬度',
  `address` varchar(200) COMMENT '操作地址',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `duration` int COMMENT '操作耗时(毫秒)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '操作状态(1:成功 0:失败)',
  `error_message` varchar(500) COMMENT '错误信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_time` (`user_id`, `operation_time`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务操作日志表';

-- =============================================
-- 8. 复合索引优化
-- =============================================
-- 运维记录表复合索引
ALTER TABLE `ywb_maintenance_record` ADD INDEX `idx_user_project_date` (`user_id`, `project_id`, `work_date`);
ALTER TABLE `ywb_maintenance_record` ADD INDEX `idx_site_date_status` (`site_id`, `work_date`, `status`);

-- 照片表复合索引
ALTER TABLE `ywb_photo` ADD INDEX `idx_business_user_time` (`business_type`, `upload_user_id`, `create_time`);

-- 业务日志表复合索引
ALTER TABLE `ywb_business_log` ADD INDEX `idx_business_operation_time` (`business_type`, `operation_type`, `operation_time`);

-- =============================================
-- 9. 字典数据初始化
-- =============================================

-- 项目状态字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('项目状态', 'ywb_project_status', 0, '运维宝项目状态', 'admin', NOW());

-- 项目状态字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '停用', '0', 'ywb_project_status', 0, '项目停用状态', 'admin', NOW()),
(2, '启用', '1', 'ywb_project_status', 0, '项目启用状态', 'admin', NOW()),
(3, '已完成', '2', 'ywb_project_status', 0, '项目完成状态', 'admin', NOW());

-- 站点状态字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('站点状态', 'ywb_site_status', 0, '运维宝站点状态', 'admin', NOW());

-- 站点状态字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '待审核', '0', 'ywb_site_status', 0, '新采集站点待审核', 'admin', NOW()),
(2, '正常', '1', 'ywb_site_status', 0, '站点正常状态', 'admin', NOW()),
(3, '停用', '2', 'ywb_site_status', 0, '站点停用状态', 'admin', NOW());

-- 站点类型字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('站点类型', 'ywb_site_type', 0, '运维宝站点类型', 'admin', NOW());

-- 站点类型字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '机房', 'computer_room', 'ywb_site_type', 0, '机房站点', 'admin', NOW()),
(2, '基站', 'base_station', 'ywb_site_type', 0, '基站站点', 'admin', NOW()),
(3, '光缆', 'optical_cable', 'ywb_site_type', 0, '光缆站点', 'admin', NOW()),
(4, '其他', 'other', 'ywb_site_type', 0, '其他类型站点', 'admin', NOW());

-- 运维记录状态字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('运维记录状态', 'ywb_maintenance_status', 0, '运维记录状态', 'admin', NOW());

-- 运维记录状态字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '进行中', '1', 'ywb_maintenance_status', 0, '运维进行中', 'admin', NOW()),
(2, '已完成', '2', 'ywb_maintenance_status', 0, '运维已完成', 'admin', NOW()),
(3, '异常', '3', 'ywb_maintenance_status', 0, '运维异常', 'admin', NOW());

-- 照片业务类型字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('照片业务类型', 'ywb_photo_business_type', 0, '照片业务类型', 'admin', NOW());

-- 照片业务类型字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '上站打卡', 'checkin', 'ywb_photo_business_type', 0, '上站打卡照片', 'admin', NOW()),
(2, '离站打卡', 'checkout', 'ywb_photo_business_type', 0, '离站打卡照片', 'admin', NOW()),
(3, '安全交底', 'briefing', 'ywb_photo_business_type', 0, '安全交底照片', 'admin', NOW()),
(4, '站点信息', 'site', 'ywb_photo_business_type', 0, '站点信息照片', 'admin', NOW());

-- AI分析类型字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('AI分析类型', 'ywb_ai_analysis_type', 0, 'AI分析类型', 'admin', NOW());

-- AI分析类型字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '人脸识别', 'face', 'ywb_ai_analysis_type', 0, '人脸识别分析', 'admin', NOW()),
(2, '身份证识别', 'id_card', 'ywb_ai_analysis_type', 0, '身份证识别分析', 'admin', NOW()),
(3, '防护用品检测', 'safety_gear', 'ywb_ai_analysis_type', 0, '防护用品检测分析', 'admin', NOW()),
(4, '场景识别', 'scene', 'ywb_ai_analysis_type', 0, '场景识别分析', 'admin', NOW());

-- =============================================
-- 10. 角色权限初始化
-- =============================================

-- 创建运维宝管理员角色
INSERT INTO `system_role` (`name`, `code`, `sort`, `data_scope`, `data_scope_dept_ids`, `status`, `type`, `remark`, `creator`, `create_time`) 
VALUES ('运维宝管理员', 'ywb_admin', 1, 1, '', 0, 2, '运维宝系统管理员', 'admin', NOW());

-- 创建运维人员角色
INSERT INTO `system_role` (`name`, `code`, `sort`, `data_scope`, `data_scope_dept_ids`, `status`, `type`, `remark`, `creator`, `create_time`) 
VALUES ('运维人员', 'ywb_worker', 2, 5, '', 0, 2, '运维宝运维人员', 'admin', NOW());

-- =============================================
-- 建表完成
-- =============================================

-- 查看创建的表
SHOW TABLES LIKE 'ywb_%';

-- 查看表结构示例
-- DESC ywb_project;
-- DESC ywb_site;
-- DESC ywb_maintenance_record;
-- DESC ywb_photo;
-- DESC ywb_ai_analysis;
-- DESC ywb_business_log;