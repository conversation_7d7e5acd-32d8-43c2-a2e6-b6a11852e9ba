<!--components/photo-upload/photo-upload.wxml-->
<view class="photo-upload-container">
  <view class="photo-grid">
    <!-- 已上传的照片 -->
    <view class="photo-item" wx:for="{{photos}}" wx:key="index">
      <image 
        class="photo-image" 
        src="{{item}}" 
        mode="aspectFill"
        bindtap="previewPhoto"
        data-index="{{index}}"
      />
      <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
        <text class="delete-icon">×</text>
      </view>
    </view>
    
    <!-- 添加照片按钮 -->
    <view 
      class="photo-add-btn" 
      wx:if="{{photos.length < maxCount}}"
      bindtap="addPhoto"
    >
      <text class="add-icon">{{addIcon}}</text>
      <text class="add-text">{{addText}}</text>
    </view>
  </view>
  
  <!-- 提示文本 -->
  <text class="upload-tip" wx:if="{{tip}}">{{tip}}</text>
</view>
