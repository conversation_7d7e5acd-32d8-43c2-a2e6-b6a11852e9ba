# 运维宝小程序业务流程图

## 1. 总体业务流程架构

```
┌─────────────────────────────────────────────────────────────┐
│                    运维宝小程序业务流程                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                     ┌─────────────┐                       │
│                     │  微信登录    │                       │
│                     └─────────────┘                       │
│                               │                          │
│                               ▼                          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   运维管理   │    │   统计分析   │    │   个人中心   │     │
│  │  (底部Tab)   │    │  (底部Tab)   │    │  (底部Tab)   │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  项目管理    │    │  历史运维    │    │  个人信息    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  新增运维    │    │  我的统计    │    │  联系客服    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                              │
│         ▼                   ▼                              │
│  ┌─────────────┐    ┌─────────────┐                       │
│  │  安全交底    │    │  按地市统计  │                       │
│  └─────────────┘    └─────────────┘                       │
│         │                   ▼                              │
│         ▼            ┌─────────────┐                       │
│  ┌─────────────┐    │  按项目统计  │                       │
│  │  上站打卡    │    └─────────────┘                       │
│  └─────────────┘                                              │
│         │                                                   │
│         ▼                                                   │
│  ┌─────────────┐                                              │
│  │  离站打卡    │                                              │
│  └─────────────┘                                              │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**说明**: 用户必须先完成微信登录才能使用小程序的所有业务功能。

### 1.1 微信登录详细流程

```
┌─────────────────────────────────────────────────────────────┐
│                    微信登录业务流程                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  点击登录    │───▶│  获取code   │───▶│  后端验证    │     │
│  │  按钮        │    │  wx.login   │    │  openid     │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  微信授权    │    │  发送请求    │    │  查询绑定    │     │
│  │  getUserInfo │    │  到后端      │    │  用户关系    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                   │          │
│                             ▼                   ▼          │
│                    ┌─────────────┐    ┌─────────────┐     │
│                    │  检查手机号  │───▶│  绑定/创建  │     │
│                    │  是否绑定    │    │  系统用户    │     │
│                    └─────────────┘    └─────────────┘     │
│                             │                              │
│                             ▼                              │
│                    ┌─────────────┐                       │
│                    │  生成JWT    │                       │
│                    │  Token       │                       │
│                    └─────────────┘                       │
│                             │                              │
│                             ▼                              │
│                    ┌─────────────┐                       │
│                    │  登录成功    │                       │
│                    │  进入主页    │                       │
│                    └─────────────┘                       │
│                                                             │
│  关键点：                                                  │
│  1. 新用户首次登录需要绑定手机号                           │
│  2. 管理员可预创建用户并绑定手机号                         │
│  3. 支持同一微信用户绑定不同系统用户（多租户）              │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心业务流程详解

### 2.1 运维管理主流程

```
┌─────────────────────────────────────────────────────────────┐
│                      运维管理主流程                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  项目管理    │───▶│  新增运维    │───▶│  安全交底    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                   │          │
│                             ▼                   ▼          │
│                    ┌─────────────┐    ┌─────────────┐     │
│                    │  上站打卡    │───▶│  离站打卡    │     │
│                    └─────────────┘    └─────────────┘     │
│                             │                              │
│                             ▼                              │
│                    ┌─────────────┐                       │
│                    │  运维记录    │                       │
│                    └─────────────┘                       │
│                                                             │
│  说明：每日必须先完成安全交底，才能进行上站打卡           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 上站打卡子流程

```
┌─────────────────────────────────────────────────────────────┐
│                    上站打卡三步骤流程                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  Step 1     │───▶│  Step 2     │───▶│  Step 3     │     │
│  │  作业内容    │    │  人脸识别    │    │  现场交底    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │• 选择作业类型│    │• 拍摄现场    │    │• 播放交底    │     │
│  │• 选择站点    │    │  照片        │    │  视频        │     │
│  │• 安全要素    │    │• AI识别      │    │• 确认观看    │     │
│  │• 定位打卡    │    │  证件和防护  │    │  完成        │     │
│  │• 500米验证  │    │  用品        │    │• 承诺遵守    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                              │
│                             ▼                              │
│                    ┌─────────────┐                       │
│                    │  AI验证失败  │                       │
│                    │  触发告警    │                       │
│                    └─────────────┘                       │
│                                                             │
│  校验：人脸识别+证件验证+防护用品检测全部通过方可进入下一步 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 站点管理流程

```
┌─────────────────────────────────────────────────────────────┐
│                     站点管理流程                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    ┌─────────────┐                         │
│                    │  站点选择    │◀──┐                    │
│                    └─────────────┘   │                    │
│                             │        │                    │
│                             ▼        │                    │
│                    ┌─────────────┐   │                    │
│                    │  搜索站点    │   │                    │
│                    └─────────────┘   │                    │
│                             │        │                    │
│                             ▼        │                    │
│  ┌─────────────┐    ┌─────────────┐   │    ┌─────────────┐ │
│  │  新增站点    │    │  站点列表    │───┘    │  编辑站点    │ │
│  └─────────────┘    └─────────────┘        └─────────────┘ │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │• 填写基本信息│    │• 显示站点    │    │• 修改站点    │     │
│  │• 定位获取    │    │  信息        │    │  信息        │     │
│  │• 提交审核    │    │• 审核状态    │    │• 重新审核    │     │
│  │(新采集状态)  │    │• 编辑删除    │    │              │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                                               │
│         ▼                                               │
│  ┌─────────────┐                                         │
│  │  后台审核    │                                         │
│  └─────────────┘                                         │
│                                                             │
│  流程：新增站点→待审核→审核通过→可用于运维                   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.4 统计分析流程

```
┌─────────────────────────────────────────────────────────────┐
│                    统计分析流程                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  历史运维    │    │  我的统计    │    │  按地市统计  │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │• 按日期展示  │    │• 按月统计    │    │• 按地市分组  │     │
│  │• 项目信息    │    │  运维数据    │    │  统计数据    │     │
│  │• 运维记录    │    │• 时长分析    │    │• 合格率计算  │     │
│  │• 核查结果    │    │• 抽查统计    │    │• 对比分析    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
│                             ▼                              │
│                    ┌─────────────┐                       │
│                    │  按项目统计  │                       │
│                    └─────────────┘                       │
│                             │                              │
│                             ▼                              │
│                    ┌─────────────┐                       │
│                    │• 项目维度    │                       │
│                    │  数据统计    │                       │
│                    │• 点位分析    │                       │
│                    │• 质量评估    │                       │
│                    └─────────────┘                       │
│                                                             │
│  特点：多维度统计分析，支持时间、地域、项目等维度          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 3. 页面菜单结构

### 3.1 底部导航菜单

```
┌─────────────────────────────────────────────────────────────┐
│                    底部Tab导航                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   🔧运维   │    │   📊统计   │    │   👤我的   │     │
│  │  (默认选中) │    │             │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
│  运维Tab：项目管理、新增运维、安全交底、上站打卡、离站打卡   │
│  统计Tab：历史运维、我的统计、按地市统计、按项目统计         │
│  我的Tab：个人信息、联系客服                                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 页面层级结构

```
├── 运维管理 (Tab首页)
│   ├── 项目管理 (列表页)
│   │   └── 项目详情 (从项目卡片进入)
│   ├── 今日运维 (详情页)
│   │   ├── 新增安全交底 (表单页)
│   │   ├── 上站打卡 (三步骤流程)
│   │   │   ├── Step1: 作业内容
│   │   │   ├── Step2: 人脸识别
│   │   │   └── Step3: 现场交底
│   │   └── 离站打卡 (拍照页)
│   └── 站点选择 (列表页)
│       ├── 站点搜索
│       └── 新增站点 (表单页)
│
├── 统计分析 (Tab首页)
│   ├── 历史运维 (列表页)
│   │   └── 运维详情 (详情页)
│   ├── 我的统计 (统计页)
│   ├── 按地市统计 (统计页)
│   └── 按项目统计 (统计页)
│
└── 个人中心 (Tab首页)
    ├── 个人信息 (展示页)
    └── 联系客服 (功能页)
```

## 4. 业务规则说明

### 4.1 核心业务规则

1. **交底优先规则**：每日必须先完成安全交底，才能进行上站打卡
2. **定位验证规则**：打卡必须在站点500米范围内，使用微信定位API
3. **AI验证规则**：人脸识别+证件验证+防护用品检测，任一失败则告警
4. **站点审核规则**：新增站点需后台审核通过后才能用于运维
5. **完整性规则**：现场交底视频必须完整观看并确认

### 4.2 数据流转规则

1. **项目-站点关联**：站点必须属于某个项目
2. **用户-项目关联**：运维人员只能操作自己参与的项目
3. **照片-业务关联**：照片必须关联到具体业务类型和ID
4. **AI分析-照片关联**：AI分析结果必须关联到照片记录

## 5. 关键校验点

### 5.1 登录与权限校验
1. **微信登录校验**：微信授权+openid验证+手机号绑定
2. **权限校验**：用户角色和项目权限
3. **多租户校验**：租户权限和数据隔离

### 5.2 业务流程校验
1. **定位校验**：500米范围验证
2. **AI校验**：人脸、证件、防护用品多重验证
3. **状态校验**：交底状态、站点状态、项目状态
4. **完整性校验**：必填字段、照片上传、视频观看

### 5.3 微信登录特有校验
1. **openid唯一性校验**：确保微信用户唯一标识
2. **手机号绑定校验**：验证手机号与系统用户匹配
3. **JWT Token有效性校验**：登录状态维护
4. **会话管理校验**：Token刷新和过期处理