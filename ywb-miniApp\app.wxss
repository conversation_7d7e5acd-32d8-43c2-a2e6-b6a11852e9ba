/**app.wxss**/
/* 全局样式 */

/* 重置样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
}

/* 容器样式 */
.container {
  padding: 0 32rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
  overflow: hidden;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-body {
  padding: 32rpx;
}

.card-footer {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #1976d2;
  color: #fff;
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-success {
  background-color: #4caf50;
  color: #fff;
}

.btn-success:hover {
  background-color: #43a047;
}

.btn-warning {
  background-color: #ff9800;
  color: #fff;
}

.btn-warning:hover {
  background-color: #f57c00;
}

.btn-danger {
  background-color: #f44336;
  color: #fff;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-secondary {
  background-color: #6c757d;
  color: #fff;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #1976d2;
  color: #1976d2;
}

.btn-outline:hover {
  background-color: #1976d2;
  color: #fff;
}

.btn-disabled {
  background-color: #e0e0e0;
  color: #9e9e9e;
  cursor: not-allowed;
}

.btn-block {
  width: 100%;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.form-label.required::before {
  content: '*';
  color: #f44336;
  margin-right: 8rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1976d2;
  outline: none;
}

.form-input.error {
  border-color: #f44336;
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

.form-error {
  color: #f44336;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 列表样式 */
.list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 28rpx;
  color: #666;
}

.list-item-action {
  margin-left: 24rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1;
}

.tag-primary {
  background-color: #e3f2fd;
  color: #1976d2;
}

.tag-success {
  background-color: #e8f5e8;
  color: #4caf50;
}

.tag-warning {
  background-color: #fff3e0;
  color: #ff9800;
}

.tag-danger {
  background-color: #ffebee;
  color: #f44336;
}

.tag-secondary {
  background-color: #f5f5f5;
  color: #666;
}

/* 步骤条样式 */
.steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  background-color: #e0e0e0;
  color: #999;
}

.step.active .step-icon {
  background-color: #1976d2;
  color: #fff;
}

.step.completed .step-icon {
  background-color: #4caf50;
  color: #fff;
}

.step-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.step.active .step-title {
  color: #1976d2;
  font-weight: 500;
}

.step.completed .step-title {
  color: #4caf50;
  font-weight: 500;
}

.step-line {
  position: absolute;
  top: 32rpx;
  left: 50%;
  width: 100%;
  height: 2rpx;
  background-color: #e0e0e0;
  z-index: -1;
}

.step:last-child .step-line {
  display: none;
}

.step.completed .step-line {
  background-color: #4caf50;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #1976d2; }
.text-success { color: #4caf50; }
.text-warning { color: #ff9800; }
.text-danger { color: #f44336; }
.text-secondary { color: #666; }
.text-muted { color: #999; }

.bg-primary { background-color: #1976d2; }
.bg-success { background-color: #4caf50; }
.bg-warning { background-color: #ff9800; }
.bg-danger { background-color: #f44336; }
.bg-secondary { background-color: #6c757d; }

.mt-8 { margin-top: 16rpx; }
.mt-16 { margin-top: 32rpx; }
.mt-24 { margin-top: 48rpx; }
.mt-32 { margin-top: 64rpx; }

.mb-8 { margin-bottom: 16rpx; }
.mb-16 { margin-bottom: 32rpx; }
.mb-24 { margin-bottom: 48rpx; }
.mb-32 { margin-bottom: 64rpx; }

.ml-8 { margin-left: 16rpx; }
.ml-16 { margin-left: 32rpx; }
.mr-8 { margin-right: 16rpx; }
.mr-16 { margin-right: 32rpx; }

.p-8 { padding: 16rpx; }
.p-16 { padding: 32rpx; }
.p-24 { padding: 48rpx; }
.p-32 { padding: 64rpx; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.hidden { display: none; }
.visible { display: block; }
