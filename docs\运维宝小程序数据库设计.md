# 运维宝小程序数据库设计

## 1. 概述

本文档基于若依框架(ruoyi-vue-pro)的现有表结构，为运维宝小程序设计业务数据库表。小程序主要功能包括：
- 用户登录认证
- 项目管理
- 站点管理
- 安全交底
- 上站/离站打卡
- 运维记录
- 照片上传与AI分析
- 统计报表

## 2. 与现有表的关联关系

### 2.1 用户体系关联

#### 2.1.1 基础用户表
- **system_users**: 复用现有用户表，存储小程序用户基本信息，通过手机号关联
- **system_dept**: 复用现有部门表，用于组织架构管理
- **system_role**: 复用现有角色表，区分运维人员、管理员等角色

#### 2.1.2 微信登录相关表
- **system_social_user**: 社交用户表，存储微信openid、昵称、头像等信息
- **system_social_user_bind**: 社交用户绑定表，关联微信用户和系统用户
- **system_social_client**: 社交客户端配置表，存储微信小程序AppID等配置
- **system_oauth2_access_token**: OAuth2访问令牌表，管理登录状态
- **system_oauth2_refresh_token**: OAuth2刷新令牌表

### 2.2 基础数据关联
- **system_dict_data**: 复用字典表，存储业务状态、类型等枚举值
- **system_operate_log**: 复用操作日志表，记录系统操作

## 3. 小程序业务表设计

### 3.1 项目管理表 (ywb_project)

**表说明**: 存储运维项目信息

```sql
CREATE TABLE `ywb_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) NOT NULL COMMENT '项目编码',
  `project_desc` text COMMENT '项目描述',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '项目状态(0:停用 1:启用 2:已完成)',
  `manager_user_id` bigint COMMENT '项目负责人ID',
  `dept_id` bigint COMMENT '所属部门ID',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_manager_user_id` (`manager_user_id`),
  KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运维项目表';
```

### 3.2 站点管理表 (ywb_site)

**表说明**: 存储运维站点信息

```sql
CREATE TABLE `ywb_site` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '站点ID',
  `site_name` varchar(100) NOT NULL COMMENT '站点名称',
  `site_code` varchar(50) NOT NULL COMMENT '站点编码',
  `site_address` varchar(200) NOT NULL COMMENT '站点地址',
  `longitude` decimal(10,7) COMMENT '经度',
  `latitude` decimal(10,7) COMMENT '纬度',
  `site_type` varchar(20) NOT NULL COMMENT '站点类型(机房/基站/光缆等)',
  `contact_person` varchar(50) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `project_id` bigint NOT NULL COMMENT '所属项目ID',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '站点状态(0:待审核 1:正常 2:停用)',
  `is_new_site` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否新采集站点',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_code` (`site_code`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_location` (`longitude`, `latitude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运维站点表';
```

### 3.3 安全交底表 (ywb_safety_briefing)

**表说明**: 存储安全交底记录

```sql
CREATE TABLE `ywb_safety_briefing` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '交底ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `briefing_date` date NOT NULL COMMENT '交底日期',
  `briefing_content` text COMMENT '交底内容',
  `video_url` varchar(500) COMMENT '交底视频URL(MinIO)',
  `briefing_person` varchar(50) NOT NULL COMMENT '交底人',
  `participants` text COMMENT '参与人员(JSON格式存储用户ID数组)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(1:有效 0:无效)',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_project_date` (`project_id`, `briefing_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安全交底表';
```

### 3.4 运维记录表 (ywb_maintenance_record)

**表说明**: 存储运维打卡记录

```sql
CREATE TABLE `ywb_maintenance_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '运维人员ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `site_id` bigint NOT NULL COMMENT '站点ID',
  `work_date` date NOT NULL COMMENT '作业日期',
  `checkin_time` datetime COMMENT '上站打卡时间',
  `checkin_longitude` decimal(10,7) COMMENT '上站打卡经度',
  `checkin_latitude` decimal(10,7) COMMENT '上站打卡纬度',
  `checkin_address` varchar(200) COMMENT '上站打卡地址',
  `checkout_time` datetime COMMENT '离站打卡时间',
  `checkout_longitude` decimal(10,7) COMMENT '离站打卡经度',
  `checkout_latitude` decimal(10,7) COMMENT '离站打卡纬度',
  `checkout_address` varchar(200) COMMENT '离站打卡地址',
  `work_duration` int COMMENT '作业时长(分钟)',
  `work_content` text COMMENT '作业内容',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '记录状态(1:进行中 2:已完成 3:异常)',
  `is_briefed` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已交底',
  `briefing_id` bigint COMMENT '关联交底ID',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`, `work_date`),
  KEY `idx_project_site` (`project_id`, `site_id`),
  KEY `idx_briefing_id` (`briefing_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运维记录表';
```

### 3.5 照片管理表 (ywb_photo)

**表说明**: 存储照片信息和MinIO URL

```sql
CREATE TABLE `ywb_photo` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型(checkin:上站 checkout:离站 briefing:交底 site:站点)',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `photo_url` varchar(500) NOT NULL COMMENT '照片URL(MinIO存储)',
  `photo_name` varchar(100) COMMENT '照片名称',
  `photo_size` bigint COMMENT '照片大小(字节)',
  `photo_type` varchar(10) COMMENT '照片类型(jpg/png等)',
  `upload_user_id` bigint NOT NULL COMMENT '上传用户ID',
  `longitude` decimal(10,7) COMMENT '拍照经度',
  `latitude` decimal(10,7) COMMENT '拍照纬度',
  `address` varchar(200) COMMENT '拍照地址',
  `is_ai_analyzed` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已AI分析',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_upload_user` (`upload_user_id`),
  KEY `idx_ai_analyzed` (`is_ai_analyzed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='照片管理表';
```

### 3.6 AI分析记录表 (ywb_ai_analysis)

**表说明**: 存储AI分析结果

```sql
CREATE TABLE `ywb_ai_analysis` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分析ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `analysis_type` varchar(20) NOT NULL COMMENT '分析类型(face:人脸识别 id_card:身份证 safety_gear:防护用品 scene:场景识别)',
  `analysis_result` json COMMENT 'AI分析结果(JSON格式)',
  `confidence_score` decimal(5,4) COMMENT '置信度分数(0-1)',
  `is_passed` bit(1) COMMENT '是否通过检测',
  `error_message` varchar(500) COMMENT '错误信息',
  `analysis_time` datetime NOT NULL COMMENT '分析时间',
  `ai_model_version` varchar(50) COMMENT 'AI模型版本',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_analysis_type` (`analysis_type`),
  KEY `idx_analysis_time` (`analysis_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI分析记录表';
```

### 3.7 统一业务日志表 (ywb_business_log)

**表说明**: 记录所有业务操作日志

```sql
CREATE TABLE `ywb_business_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NOT NULL COMMENT '操作用户ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型(project:项目 site:站点 maintenance:运维 briefing:交底)',
  `business_id` bigint COMMENT '业务ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型(create:创建 update:更新 delete:删除 checkin:上站 checkout:离站)',
  `operation_desc` varchar(200) NOT NULL COMMENT '操作描述',
  `request_params` json COMMENT '请求参数(JSON格式)',
  `response_result` json COMMENT '响应结果(JSON格式)',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `longitude` decimal(10,7) COMMENT '操作位置经度',
  `latitude` decimal(10,7) COMMENT '操作位置纬度',
  `address` varchar(200) COMMENT '操作地址',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `duration` int COMMENT '操作耗时(毫秒)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '操作状态(1:成功 0:失败)',
  `error_message` varchar(500) COMMENT '错误信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_time` (`user_id`, `operation_time`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务操作日志表';
```

## 4. 字典数据配置

### 4.1 项目状态字典

```sql
-- 项目状态字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('项目状态', 'ywb_project_status', 0, '运维宝项目状态', 'admin', NOW());

-- 项目状态字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '停用', '0', 'ywb_project_status', 0, '项目停用状态', 'admin', NOW()),
(2, '启用', '1', 'ywb_project_status', 0, '项目启用状态', 'admin', NOW()),
(3, '已完成', '2', 'ywb_project_status', 0, '项目完成状态', 'admin', NOW());
```

### 4.2 站点状态字典

```sql
-- 站点状态字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('站点状态', 'ywb_site_status', 0, '运维宝站点状态', 'admin', NOW());

-- 站点状态字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '待审核', '0', 'ywb_site_status', 0, '新采集站点待审核', 'admin', NOW()),
(2, '正常', '1', 'ywb_site_status', 0, '站点正常状态', 'admin', NOW()),
(3, '停用', '2', 'ywb_site_status', 0, '站点停用状态', 'admin', NOW());
```

### 4.3 站点类型字典

```sql
-- 站点类型字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('站点类型', 'ywb_site_type', 0, '运维宝站点类型', 'admin', NOW());

-- 站点类型字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `remark`, `creator`, `create_time`) VALUES
(1, '机房', 'computer_room', 'ywb_site_type', 0, '机房站点', 'admin', NOW()),
(2, '基站', 'base_station', 'ywb_site_type', 0, '基站站点', 'admin', NOW()),
(3, '光缆', 'optical_cable', 'ywb_site_type', 0, '光缆站点', 'admin', NOW()),
(4, '其他', 'other', 'ywb_site_type', 0, '其他类型站点', 'admin', NOW());
```

## 5. 索引优化建议

### 5.1 复合索引

```sql
-- 运维记录表复合索引
ALTER TABLE `ywb_maintenance_record` ADD INDEX `idx_user_project_date` (`user_id`, `project_id`, `work_date`);
ALTER TABLE `ywb_maintenance_record` ADD INDEX `idx_site_date_status` (`site_id`, `work_date`, `status`);

-- 照片表复合索引
ALTER TABLE `ywb_photo` ADD INDEX `idx_business_user_time` (`business_type`, `upload_user_id`, `create_time`);

-- 业务日志表复合索引
ALTER TABLE `ywb_business_log` ADD INDEX `idx_business_operation_time` (`business_type`, `operation_type`, `operation_time`);
```

## 6. 数据迁移和初始化

### 6.1 初始化管理员角色

```sql
-- 创建运维宝管理员角色
INSERT INTO `system_role` (`name`, `code`, `sort`, `data_scope`, `data_scope_dept_ids`, `status`, `type`, `remark`, `creator`, `create_time`) 
VALUES ('运维宝管理员', 'ywb_admin', 1, 1, '', 0, 2, '运维宝系统管理员', 'admin', NOW());

-- 创建运维人员角色
INSERT INTO `system_role` (`name`, `code`, `sort`, `data_scope`, `data_scope_dept_ids`, `status`, `type`, `remark`, `creator`, `create_time`) 
VALUES ('运维人员', 'ywb_worker', 2, 5, '', 0, 2, '运维宝运维人员', 'admin', NOW());
```

## 7. 注意事项

### 7.1 MinIO存储
- 照片和视频文件存储在MinIO中
- 数据库只存储文件的URL路径
- 建议按日期和业务类型组织MinIO的bucket结构

### 7.2 地理位置
- 经纬度使用decimal(10,7)精度，满足GPS定位需求
- 建议添加地理位置索引以支持附近站点查询

### 7.3 AI分析
- AI分析结果使用JSON格式存储，便于扩展
- 支持多种AI分析类型，可根据业务需求扩展

### 7.4 性能优化
- 大表建议按时间分区
- 历史数据可考虑归档策略
- 重要查询添加合适的复合索引

### 7.5 数据安全
- 敏感数据建议加密存储
- 定期备份重要业务数据
- 设置合理的数据保留策略

### 7.6 外键约束说明
- 本设计采用逻辑外键，不使用物理外键约束
- 通过应用层保证数据一致性
- 便于数据库维护和性能优化

### 7.7 微信登录实现说明

#### 7.7.1 登录流程
1. 小程序调用 `wx.login()` 获取 `code`
2. 后端使用 `code` 向微信服务器获取 `openid` 和 `session_key`
3. 根据 `openid` 查询 `system_social_user` 表
4. 如果存在社交用户，通过 `system_social_user_bind` 获取系统用户
5. 如果不存在，创建社交用户记录并绑定到现有系统用户（通过手机号匹配）
6. 生成 JWT token 返回给小程序

#### 7.7.2 用户绑定策略
- 新用户首次登录需要绑定手机号
- 后台管理员可在系统中预创建用户，绑定手机号
- 支持同一微信用户绑定不同系统用户（多租户场景）

#### 7.7.3 关键表字段说明
```sql
-- system_social_user 表关键字段
- openid: 微信用户唯一标识
- nickname: 微信昵称
- avatar: 微信头像URL
- token: 微信访问令牌
- raw_user_info: 原始用户信息JSON

-- system_social_user_bind 表关键字段
- user_id: 系统用户ID
- social_user_id: 社交用户ID
- user_type: 用户类型（默认为1-管理后台用户）

-- system_social_client 配置示例
- name: "运维宝小程序"
- social_type: 34 (微信小程序)
- client_id: 微信小程序AppID
- client_secret: 微信小程序AppSecret
```