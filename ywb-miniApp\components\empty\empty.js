// components/empty/empty.js
Component({
  properties: {
    // 是否显示
    show: {
      type: Boolean,
      value: true
    },
    // 图标
    icon: {
      type: String,
      value: '📝'
    },
    // 主要文本
    text: {
      type: String,
      value: '暂无数据'
    },
    // 描述文本
    desc: {
      type: String,
      value: ''
    },
    // 按钮文本
    buttonText: {
      type: String,
      value: ''
    }
  },

  data: {},

  methods: {
    // 按钮点击事件
    onButtonTap() {
      this.triggerEvent('buttontap')
    }
  }
})
