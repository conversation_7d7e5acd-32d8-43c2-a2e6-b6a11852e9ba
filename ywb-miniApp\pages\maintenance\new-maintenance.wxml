<!--pages/maintenance/new-maintenance.wxml-->
<view class="maintenance-container">
  <!-- 项目信息卡片 -->
  <view class="project-card">
    <view class="project-header">
      <text class="project-name">{{projectInfo.projectName}}</text>
      <text class="project-detail-btn" bindtap="viewProjectDetail">详情 ></text>
    </view>
    <view class="project-info">
      <text class="project-desc">{{projectInfo.projectDesc || '暂无描述'}}</text>
      <text class="project-period">{{projectInfo.startDate}} - {{projectInfo.endDate || '进行中'}}</text>
    </view>
  </view>

  <!-- 安全交底区域 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">安全交底</text>
      <view class="briefing-status {{todayBriefing ? 'completed' : 'pending'}}">
        <text class="status-icon">{{todayBriefing ? '✓' : '⚠'}}</text>
        <text class="status-text">{{todayBriefing ? '已完成' : '未完成'}}</text>
      </view>
    </view>

    <!-- 已交底状态 -->
    <view class="briefing-completed" wx:if="{{todayBriefing}}">
      <view class="briefing-info">
        <image class="briefing-image" src="{{todayBriefing.photoUrl}}" mode="aspectFill" bindtap="previewBriefingPhoto"></image>
        <view class="briefing-details">
          <text class="briefing-title">{{todayBriefing.briefingName}}</text>
          <text class="briefing-time">交底时间：{{todayBriefing.briefingTime}}</text>
          <text class="briefing-person">交底人员：{{todayBriefing.briefingPerson}}</text>
          <text class="briefing-location">交底地点：{{todayBriefing.briefingLocation}}</text>
        </view>
      </view>
      <view class="briefing-participants" wx:if="{{todayBriefing.participants.length > 0}}">
        <text class="participants-label">参与人员：</text>
        <text class="participants-list">{{todayBriefing.participantsText}}</text>
      </view>
    </view>

    <!-- 未交底状态 -->
    <view class="briefing-pending" wx:else>
      <view class="pending-notice">
        <text class="notice-icon">⚠️</text>
        <text class="notice-text">您今日未安全交底，请上站打卡前先安全交底</text>
      </view>
      <button class="briefing-btn" bindtap="goToBriefing">
        <text class="btn-icon">🛡️</text>
        <text class="btn-text">安全交底</text>
      </button>
    </view>
  </view>

  <!-- 运维记录区域 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">运维记录</text>
      <text class="record-count">今日{{todayRecords.length}}条记录</text>
    </view>

    <!-- 新增运维按钮 -->
    <view class="new-maintenance-action">
      <button 
        class="new-maintenance-btn {{!todayBriefing ? 'disabled' : ''}}" 
        bindtap="startMaintenance"
        disabled="{{!todayBriefing}}"
      >
        <text class="btn-icon">📍</text>
        <text class="btn-text">{{!todayBriefing ? '请先完成安全交底' : '+ 上站打卡'}}</text>
      </button>
    </view>

    <!-- 今日运维记录列表 -->
    <view class="maintenance-records" wx:if="{{todayRecords.length > 0}}">
      <view class="record-item" wx:for="{{todayRecords}}" wx:key="id">
        <view class="record-header">
          <text class="site-name">{{item.siteName}}</text>
          <view class="record-status {{item.status === 1 ? 'in-progress' : item.status === 2 ? 'completed' : 'abnormal'}}">
            <text class="status-dot"></text>
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>
        
        <view class="record-details">
          <view class="record-time">
            <text class="time-label">上站时间：</text>
            <text class="time-value">{{item.checkinTime || '未打卡'}}</text>
          </view>
          <view class="record-time" wx:if="{{item.checkoutTime}}">
            <text class="time-label">离站时间：</text>
            <text class="time-value">{{item.checkoutTime}}</text>
          </view>
          <view class="record-duration" wx:if="{{item.workDuration}}">
            <text class="duration-label">作业时长：</text>
            <text class="duration-value">{{item.workDurationText}}</text>
          </view>
        </view>

        <view class="record-actions">
          <!-- 进行中的记录显示离站打卡按钮 -->
          <button 
            class="action-btn checkout-btn" 
            wx:if="{{item.status === 1}}"
            bindtap="goToCheckout"
            data-record="{{item}}"
          >
            离站打卡
          </button>
          
          <!-- 已完成的记录显示查看详情按钮 -->
          <button 
            class="action-btn detail-btn" 
            wx:if="{{item.status === 2}}"
            bindtap="viewRecordDetail"
            data-record="{{item}}"
          >
            查看详情
          </button>
          
          <!-- 异常记录显示处理按钮 -->
          <button 
            class="action-btn handle-btn" 
            wx:if="{{item.status === 3}}"
            bindtap="handleAbnormal"
            data-record="{{item}}"
          >
            处理异常
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-records" wx:if="{{todayRecords.length === 0 && todayBriefing}}">
      <image class="empty-image" src="/images/empty-maintenance.png" mode="aspectFit"></image>
      <text class="empty-text">今日暂无运维记录</text>
      <text class="empty-desc">点击上方按钮开始运维工作</text>
    </view>
  </view>

  <!-- 历史记录快捷入口 -->
  <view class="quick-links">
    <view class="link-item" bindtap="goToHistory">
      <text class="link-icon">📋</text>
      <text class="link-text">历史运维</text>
      <text class="link-arrow">></text>
    </view>
    <view class="link-item" bindtap="goToStatistics">
      <text class="link-icon">📊</text>
      <text class="link-text">运维统计</text>
      <text class="link-arrow">></text>
    </view>
    <view class="link-item" bindtap="goToSiteManage">
      <text class="link-icon">🏢</text>
      <text class="link-text">站点管理</text>
      <text class="link-arrow">></text>
    </view>
  </view>
</view>
