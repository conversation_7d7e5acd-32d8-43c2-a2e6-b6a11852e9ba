// pages/maintenance/checkin-step3.js
const app = getApp()
const { maintenanceAPI, photoAPI, aiAPI } = require('../../utils/api')
const { formatTime, chooseImage, previewImage, showError, showSuccess } = require('../../utils/util')

Page({
  data: {
    stepData: null,
    
    // 显示数据
    workTypeText: '',
    safetyFactorsText: '',
    checkinTime: '',
    
    // 照片数据
    scenePhotos: [],
    safetyGearPhotos: [],
    
    // 表单数据
    workRemark: '',
    
    // AI检测结果
    aiDetectionResults: [],
    
    // 状态
    canComplete: false,
    isSubmitting: false
  },

  onLoad() {
    console.log('上站打卡步骤3加载')
    
    // 获取步骤数据
    const stepData = wx.getStorageSync('checkinStepData')
    if (!stepData) {
      showError('数据异常，请重新开始')
      wx.navigateBack()
      return
    }
    
    this.setData({ stepData })
    this.initDisplayData()
  },

  // 初始化显示数据
  initDisplayData() {
    const { stepData } = this.data
    
    // 作业类型文本
    const workTypeText = stepData.workType === 'repair' ? '配套维修' : '杆塔维修'
    
    // 安全要素文本
    let safetyFactorsText = ''
    if (stepData.safetyFactors) {
      const factors = stepData.safetyFactors.split(',')
      const factorTexts = factors.map(factor => {
        switch (factor) {
          case 'electric': return '涉电'
          case 'height': return '登高'
          default: return factor
        }
      })
      safetyFactorsText = factorTexts.join('、')
    }
    
    // 打卡时间
    const checkinTime = formatTime(new Date(), 'YYYY-MM-DD HH:mm:ss')
    
    this.setData({
      workTypeText,
      safetyFactorsText,
      checkinTime
    })
    
    this.checkCanComplete()
  },

  // 拍照
  takePhoto(e) {
    const type = e.currentTarget.dataset.type
    
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
        
        chooseImage(1, ['compressed'], sourceType)
          .then(tempFilePaths => {
            this.uploadPhoto(tempFilePaths[0], type)
          })
          .catch(error => {
            console.error('选择图片失败:', error)
          })
      }
    })
  },

  // 上传照片
  uploadPhoto(filePath, type) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })
    
    const businessType = 'checkin'
    const businessId = this.data.stepData.projectId
    
    photoAPI.uploadPhoto(filePath, businessType, businessId)
      .then(res => {
        wx.hideLoading()
        
        const photoUrl = res.data.photoUrl
        
        if (type === 'scene') {
          const scenePhotos = [...this.data.scenePhotos, photoUrl]
          this.setData({ scenePhotos })
        } else if (type === 'gear') {
          const safetyGearPhotos = [...this.data.safetyGearPhotos, photoUrl]
          this.setData({ safetyGearPhotos })
        }
        
        this.checkCanComplete()
        showSuccess('照片上传成功')
        
        // 触发AI检测
        this.performAIDetection(photoUrl, type)
      })
      .catch(error => {
        wx.hideLoading()
        console.error('照片上传失败:', error)
        showError('照片上传失败，请重试')
      })
  },

  // 执行AI检测
  performAIDetection(photoUrl, type) {
    const detectionType = type === 'scene' ? 'scene_safety' : 'safety_gear'
    
    aiAPI.safetyDetection(photoUrl, detectionType)
      .then(res => {
        const result = res.data
        
        // 添加检测结果
        const newResult = {
          id: Date.now(),
          typeName: type === 'scene' ? '现场安全检测' : '防护用品检测',
          status: result.status, // pass, warning, fail
          statusText: this.getStatusText(result.status),
          description: result.description
        }
        
        const aiDetectionResults = [...this.data.aiDetectionResults, newResult]
        this.setData({ aiDetectionResults })
      })
      .catch(error => {
        console.error('AI检测失败:', error)
        // AI检测失败不影响主流程，只记录日志
      })
  },

  // 获取状态文本
  getStatusText(status) {
    switch (status) {
      case 'pass': return '检测通过'
      case 'warning': return '存在风险'
      case 'fail': return '检测不通过'
      default: return '未知状态'
    }
  },

  // 预览照片
  previewPhoto(e) {
    const urls = e.currentTarget.dataset.urls
    const current = e.currentTarget.dataset.current
    
    previewImage(urls, current)
  },

  // 删除照片
  deletePhoto(e) {
    const type = e.currentTarget.dataset.type
    const index = e.currentTarget.dataset.index
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？',
      success: (res) => {
        if (res.confirm) {
          if (type === 'scene') {
            const scenePhotos = [...this.data.scenePhotos]
            scenePhotos.splice(index, 1)
            this.setData({ scenePhotos })
          } else if (type === 'gear') {
            const safetyGearPhotos = [...this.data.safetyGearPhotos]
            safetyGearPhotos.splice(index, 1)
            this.setData({ safetyGearPhotos })
          }
          
          this.checkCanComplete()
        }
      }
    })
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({ workRemark: e.detail.value })
  },

  // 检查是否可以完成
  checkCanComplete() {
    const { scenePhotos, safetyGearPhotos } = this.data
    
    const canComplete = scenePhotos.length > 0 && safetyGearPhotos.length > 0
    
    this.setData({ canComplete })
  },

  // 上一步
  prevStep() {
    wx.navigateBack()
  },

  // 完成打卡
  completeCheckin() {
    if (!this.data.canComplete || this.data.isSubmitting) {
      return
    }
    
    this.setData({ isSubmitting: true })
    
    const {
      stepData,
      scenePhotos,
      safetyGearPhotos,
      workRemark,
      checkinTime,
      aiDetectionResults
    } = this.data
    
    // 构建打卡数据
    const checkinData = {
      projectId: stepData.projectId,
      briefingId: stepData.briefingId,
      siteId: stepData.siteId,
      workType: stepData.workType,
      safetyFactors: stepData.safetyFactors,
      checkinTime,
      checkinLocation: stepData.checkinLocation,
      faceRecognition: stepData.faceRecognition,
      scenePhotos,
      safetyGearPhotos,
      workRemark: workRemark.trim(),
      aiDetectionResults
    }
    
    // 调用API提交打卡
    maintenanceAPI.createMaintenanceRecord(checkinData)
      .then(res => {
        // 清除临时数据
        wx.removeStorageSync('checkinStepData')
        
        showSuccess('上站打卡成功')
        
        // 延迟跳转到运维记录页面
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/maintenance/record-detail?recordId=${res.data.recordId}`
          })
        }, 1500)
      })
      .catch(error => {
        console.error('上站打卡失败:', error)
        showError(error.message || '打卡失败，请重试')
      })
      .finally(() => {
        this.setData({ isSubmitting: false })
      })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '现场交底打卡 - 运维宝',
      path: '/pages/maintenance/checkin-step3',
      imageUrl: '/images/share-checkin-complete.png'
    }
  }
})
