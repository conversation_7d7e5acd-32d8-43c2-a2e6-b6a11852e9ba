// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'https://api.yunweibao.com', // 后端API地址
    version: '1.0.0'
  },

  onLaunch() {
    console.log('运维宝小程序启动')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 检查更新
    this.checkForUpdate()
  },

  onShow() {
    console.log('运维宝小程序显示')
  },

  onHide() {
    console.log('运维宝小程序隐藏')
  },

  onError(msg) {
    console.error('运维宝小程序错误:', msg)
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
      console.log('用户已登录:', userInfo)
    } else {
      console.log('用户未登录，跳转到登录页')
      wx.reLaunch({
        url: '/pages/login/login'
      })
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请检查网络后重试',
          showCancel: false
        })
      })
    }
  },

  // 全局登录方法
  login(callback) {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 调用后端登录接口
          wx.request({
            url: `${this.globalData.baseUrl}/api/auth/wechat-login`,
            method: 'POST',
            data: {
              code: res.code
            },
            success: (response) => {
              if (response.data.success) {
                const { token, userInfo } = response.data.data
                
                // 保存登录信息
                wx.setStorageSync('token', token)
                wx.setStorageSync('userInfo', userInfo)
                
                this.globalData.token = token
                this.globalData.userInfo = userInfo
                
                if (callback) callback(true, userInfo)
              } else {
                if (callback) callback(false, response.data.message)
              }
            },
            fail: (error) => {
              console.error('登录请求失败:', error)
              if (callback) callback(false, '网络请求失败')
            }
          })
        } else {
          console.error('获取微信登录code失败:', res.errMsg)
          if (callback) callback(false, '微信登录失败')
        }
      },
      fail: (error) => {
        console.error('微信登录失败:', error)
        if (callback) callback(false, '微信登录失败')
      }
    })
  },

  // 全局退出登录方法
  logout() {
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    this.globalData.token = null
    this.globalData.userInfo = null
    
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 全局网络请求方法
  request(options) {
    const { url, method = 'GET', data = {}, success, fail } = options
    
    wx.request({
      url: `${this.globalData.baseUrl}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.globalData.token || ''}`
      },
      success: (res) => {
        if (res.statusCode === 401) {
          // token过期，重新登录
          this.logout()
          return
        }
        
        if (success) success(res)
      },
      fail: (error) => {
        console.error('网络请求失败:', error)
        if (fail) fail(error)
      }
    })
  }
})
