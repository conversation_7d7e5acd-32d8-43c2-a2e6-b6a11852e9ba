/* pages/login/login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.login-header {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  height: 88rpx;
  position: relative;
}

.back-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
}

/* 主要内容 */
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 64rpx;
}

/* LOGO区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}

.logo-container {
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.logo-image {
  width: 96rpx;
  height: 96rpx;
}

.app-name {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 登录按钮区域 */
.login-actions {
  margin-bottom: 80rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.wechat-login-btn {
  background: #07c160;
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.4);
}

.wechat-login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.4);
}

.wechat-login-btn[disabled] {
  background: #a0a0a0;
  box-shadow: none;
  transform: none;
}

.btn-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 提示信息 */
.login-tips {
  text-align: center;
  margin-bottom: 40rpx;
}

.tip-text {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

/* 手机号绑定弹窗 */
.phone-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.phone-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.phone-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
}

.modal-body {
  padding: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 32rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1976d2;
}

.code-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.code-input {
  flex: 1;
}

.code-btn {
  width: 160rpx;
  height: 88rpx;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-btn.disabled {
  background: #e0e0e0;
  color: #999;
}

.modal-footer {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #1976d2;
  color: #fff;
}

.confirm-btn[disabled] {
  background: #e0e0e0;
  color: #999;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
}

.version-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.logo-section {
  animation: fadeInUp 0.8s ease-out;
}

.login-actions {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.login-tips {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}
