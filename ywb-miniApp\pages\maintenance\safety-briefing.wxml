<!--pages/maintenance/safety-briefing.wxml-->
<view class="briefing-container">
  <!-- 提示信息 -->
  <view class="briefing-tip">
    <text class="tip-icon">💡</text>
    <text class="tip-text">每日出工前需要做一次安全交底</text>
  </view>

  <!-- 基本信息 -->
  <view class="info-section">
    <view class="info-row">
      <text class="info-label">交底时间：</text>
      <text class="info-value">{{briefingDate}}</text>
    </view>
    <view class="info-row">
      <text class="info-label">交底人员：</text>
      <text class="info-value">{{userInfo.nickname || userInfo.username}}</text>
    </view>
  </view>

  <!-- 表单区域 -->
  <form bindsubmit="submitBriefing">
    <view class="form-section">
      <!-- 管理区域 -->
      <view class="form-group">
        <text class="form-label required">管理区域</text>
        <picker 
          mode="selector" 
          range="{{areaList}}" 
          range-key="name"
          value="{{areaIndex}}" 
          bindchange="onAreaChange"
        >
          <view class="form-picker {{areaIndex === -1 ? 'placeholder' : ''}}">
            <text>{{areaIndex === -1 ? '请选择管理区域' : areaList[areaIndex].name}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 交底名称 -->
      <view class="form-group">
        <text class="form-label required">交底名称</text>
        <input 
          class="form-input" 
          placeholder="请输入交底名称（上限50字）"
          value="{{briefingName}}"
          bindinput="onBriefingNameInput"
          maxlength="50"
        />
        <text class="input-counter">{{briefingName.length}}/50</text>
      </view>

      <!-- 交底地点 -->
      <view class="form-group">
        <text class="form-label required">交底地点</text>
        <input 
          class="form-input" 
          placeholder="请输入交底地点（上限50字）"
          value="{{briefingLocation}}"
          bindinput="onBriefingLocationInput"
          maxlength="50"
        />
        <text class="input-counter">{{briefingLocation.length}}/50</text>
      </view>

      <!-- 备注 -->
      <view class="form-group">
        <text class="form-label">备注</text>
        <textarea 
          class="form-textarea" 
          placeholder="请输入备注信息（上限200字）"
          value="{{briefingRemark}}"
          bindinput="onBriefingRemarkInput"
          maxlength="200"
          auto-height
        />
        <text class="input-counter">{{briefingRemark.length}}/200</text>
      </view>
    </view>

    <!-- 拍摄区域 -->
    <view class="photo-section">
      <view class="section-title">
        <text class="title-text">拍摄安全交底资料</text>
        <text class="title-desc">请按要求拍摄相关照片</text>
      </view>

      <!-- 安全交底记录单 -->
      <view class="photo-group">
        <view class="photo-header">
          <text class="photo-label required">安全交底记录单</text>
          <text class="photo-desc">请拍摄完整的交底记录单</text>
        </view>
        <view class="photo-upload-area">
          <view class="photo-item" wx:if="{{briefingRecordPhoto}}">
            <image 
              class="photo-image" 
              src="{{briefingRecordPhoto}}" 
              mode="aspectFill"
              bindtap="previewPhoto"
              data-url="{{briefingRecordPhoto}}"
            />
            <view class="photo-delete" bindtap="deletePhoto" data-type="record">
              <text class="delete-icon">×</text>
            </view>
          </view>
          <view class="photo-upload-btn" wx:else bindtap="takePhoto" data-type="record">
            <text class="upload-icon">📷</text>
            <text class="upload-text">拍摄记录单</text>
          </view>
        </view>
      </view>

      <!-- 防护用品 -->
      <view class="photo-group">
        <view class="photo-header">
          <text class="photo-label required">防护用品</text>
          <text class="photo-desc">安全帽、安全带、电笔、绝缘手套</text>
        </view>
        <view class="photo-upload-area">
          <view class="photo-item" wx:if="{{safetyGearPhoto}}">
            <image 
              class="photo-image" 
              src="{{safetyGearPhoto}}" 
              mode="aspectFill"
              bindtap="previewPhoto"
              data-url="{{safetyGearPhoto}}"
            />
            <view class="photo-delete" bindtap="deletePhoto" data-type="gear">
              <text class="delete-icon">×</text>
            </view>
          </view>
          <view class="photo-upload-btn" wx:else bindtap="takePhoto" data-type="gear">
            <text class="upload-icon">📷</text>
            <text class="upload-text">拍摄防护用品</text>
          </view>
        </view>
        <view class="safety-gear-tip">
          <text class="tip-text">💡 需拍摄安全帽、安全带、电笔、绝缘手套</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn {{canSubmit ? '' : 'disabled'}}" 
        form-type="submit"
        disabled="{{!canSubmit || isSubmitting}}"
      >
        <text class="btn-text">{{isSubmitting ? '提交中...' : '提交'}}</text>
      </button>
    </view>
  </form>

  <!-- 加载遮罩 -->
  <view class="loading-mask" wx:if="{{isSubmitting}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在提交交底信息...</text>
    </view>
  </view>
</view>
