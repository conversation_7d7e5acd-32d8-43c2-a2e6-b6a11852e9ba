/* pages/statistics/statistics.wxss */
.statistics-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
}

/* 统计卡片 */
.stats-cards {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.stats-card {
  flex: 1;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.card-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 8rpx;
}

.card-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.card-unit {
  font-size: 20rpx;
  color: #999;
}

/* 快速入口 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.action-item {
  flex: 1;
  min-width: calc(50% - 8rpx);
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.12);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.action-icon.history {
  background: #e3f2fd;
}

.action-icon.personal {
  background: #e8f5e8;
}

.action-icon.city {
  background: #fff3e0;
}

.action-icon.project {
  background: #f3e5f5;
}

.action-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 最近运维记录 */
.recent-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #1976d2;
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx;
  gap: 24rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.empty-icon {
  font-size: 120rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 记录列表 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.record-item {
  padding: 24rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.record-item:active {
  background: #f0f0f0;
  transform: translateY(1rpx);
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.record-site {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.record-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.record-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.record-status.ongoing {
  background: #fff3e0;
  color: #ff9800;
}

.record-status.exception {
  background: #ffebee;
  color: #f44336;
}

.record-content {
  margin-bottom: 12rpx;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.info-item {
  font-size: 24rpx;
  color: #666;
}

.info-separator {
  font-size: 20rpx;
  color: #ccc;
}

.record-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-label {
  font-size: 24rpx;
  color: #666;
}

.time-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.record-footer {
  border-top: 1rpx solid #e0e0e0;
  padding-top: 12rpx;
}

.project-name {
  font-size: 22rpx;
  color: #999;
}

/* 统计图表 */
.chart-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.chart-tabs {
  display: flex;
  gap: 16rpx;
}

.tab-item {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #1976d2;
  color: #fff;
}

.chart-container {
  margin-top: 24rpx;
  height: 300rpx;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
  height: 100%;
  width: 100%;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #1976d2, #42a5f5);
  border-radius: 4rpx 4rpx 0 0;
  min-height: 8rpx;
  transition: height 0.5s ease;
}

.bar-value {
  position: absolute;
  top: -32rpx;
  font-size: 20rpx;
  color: #333;
  font-weight: 500;
}

.bar-label {
  margin-top: 8rpx;
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

/* 运维类型分布 */
.type-distribution {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.distribution-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-info {
  min-width: 120rpx;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.type-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.type-count {
  font-size: 22rpx;
  color: #666;
}

.type-progress {
  flex: 1;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #1976d2, #42a5f5);
  border-radius: 8rpx;
  transition: width 0.5s ease;
}

.type-percentage {
  min-width: 60rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-cards {
    flex-direction: column;
  }
  
  .stats-card {
    flex: none;
  }
  
  .action-item {
    min-width: calc(50% - 8rpx);
  }
}
