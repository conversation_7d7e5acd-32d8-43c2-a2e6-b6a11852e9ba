// pages/login/login.js
const app = getApp()
const { authAPI } = require('../../utils/api')
const { validatePhone, showSuccess, showError } = require('../../utils/util')

Page({
  data: {
    isLoading: false,
    showBack: false,
    version: '1.0.0',
    
    // 手机号绑定相关
    showPhoneModal: false,
    phoneNumber: '',
    verifyCode: '',
    isBinding: false,
    canSendCode: true,
    codeButtonText: '发送验证码',
    countdown: 0,
    
    // 临时存储微信登录信息
    tempWechatInfo: null
  },

  onLoad(options) {
    console.log('登录页面加载')
    
    // 检查是否需要显示返回按钮
    if (options.from) {
      this.setData({
        showBack: true
      })
    }
    
    // 获取版本信息
    this.setData({
      version: app.globalData.version
    })
  },

  onShow() {
    // 检查是否已经登录
    if (app.globalData.token && app.globalData.userInfo) {
      this.redirectToHome()
    }
  },

  // 微信登录
  handleWechatLogin() {
    if (this.data.isLoading) return
    
    this.setData({ isLoading: true })
    
    wx.login({
      success: (res) => {
        if (res.code) {
          this.callWechatLoginAPI(res.code)
        } else {
          showError('获取微信登录凭证失败')
          this.setData({ isLoading: false })
        }
      },
      fail: (error) => {
        console.error('微信登录失败:', error)
        showError('微信登录失败')
        this.setData({ isLoading: false })
      }
    })
  },

  // 调用微信登录API
  callWechatLoginAPI(code) {
    authAPI.wechatLogin(code)
      .then((res) => {
        const { needBindPhone, userInfo, token, openid } = res.data
        
        if (needBindPhone) {
          // 需要绑定手机号
          this.setData({
            tempWechatInfo: { openid, userInfo },
            showPhoneModal: true,
            isLoading: false
          })
        } else {
          // 登录成功
          this.handleLoginSuccess(token, userInfo)
        }
      })
      .catch((error) => {
        console.error('微信登录API调用失败:', error)
        showError(error.message || '登录失败，请重试')
        this.setData({ isLoading: false })
      })
  },

  // 登录成功处理
  handleLoginSuccess(token, userInfo) {
    // 保存登录信息
    wx.setStorageSync('token', token)
    wx.setStorageSync('userInfo', userInfo)
    
    app.globalData.token = token
    app.globalData.userInfo = userInfo
    
    showSuccess('登录成功')
    
    // 跳转到首页
    setTimeout(() => {
      this.redirectToHome()
    }, 1500)
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      phoneNumber: e.detail.value
    })
    
    // 检查是否可以发送验证码
    this.checkCanSendCode()
  },

  // 验证码输入
  onCodeInput(e) {
    this.setData({
      verifyCode: e.detail.value
    })
  },

  // 检查是否可以发送验证码
  checkCanSendCode() {
    const { phoneNumber, countdown } = this.data
    const canSend = validatePhone(phoneNumber) && countdown === 0
    
    this.setData({
      canSendCode: canSend
    })
  },

  // 发送验证码
  sendVerifyCode() {
    const { phoneNumber, canSendCode } = this.data
    
    if (!canSendCode) return
    
    if (!validatePhone(phoneNumber)) {
      showError('请输入正确的手机号码')
      return
    }
    
    // 调用发送验证码API
    authAPI.sendVerifyCode(phoneNumber)
      .then(() => {
        showSuccess('验证码已发送')
        this.startCountdown()
      })
      .catch((error) => {
        showError(error.message || '发送验证码失败')
      })
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    
    this.setData({
      countdown,
      canSendCode: false,
      codeButtonText: `${countdown}s后重发`
    })
    
    const timer = setInterval(() => {
      countdown--
      
      if (countdown > 0) {
        this.setData({
          countdown,
          codeButtonText: `${countdown}s后重发`
        })
      } else {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeButtonText: '发送验证码'
        })
        this.checkCanSendCode()
      }
    }, 1000)
  },

  // 确认绑定手机号
  confirmBindPhone() {
    const { phoneNumber, verifyCode, isBinding, tempWechatInfo } = this.data
    
    if (isBinding) return
    
    if (!validatePhone(phoneNumber)) {
      showError('请输入正确的手机号码')
      return
    }
    
    if (!verifyCode || verifyCode.length !== 6) {
      showError('请输入6位验证码')
      return
    }
    
    this.setData({ isBinding: true })
    
    // 调用绑定手机号API
    authAPI.bindPhone({
      phoneNumber,
      verifyCode,
      openid: tempWechatInfo.openid
    })
      .then((res) => {
        const { token, userInfo } = res.data
        
        showSuccess('绑定成功')
        
        // 关闭弹窗
        this.closePhoneModal()
        
        // 登录成功
        this.handleLoginSuccess(token, userInfo)
      })
      .catch((error) => {
        showError(error.message || '绑定失败，请重试')
        this.setData({ isBinding: false })
      })
  },

  // 关闭手机号绑定弹窗
  closePhoneModal() {
    this.setData({
      showPhoneModal: false,
      phoneNumber: '',
      verifyCode: '',
      isBinding: false,
      tempWechatInfo: null,
      isLoading: false
    })
  },

  // 阻止弹窗滚动穿透
  preventTouchMove() {
    return false
  },

  onUnload() {
    // 清理定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  }
})
