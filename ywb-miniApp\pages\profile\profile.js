// pages/profile/profile.js
const app = getApp()
const { authAPI, statisticsAPI } = require('../../utils/api')
const { formatDuration, showError, showSuccess, showModal } = require('../../utils/util')

Page({
  data: {
    userInfo: {},
    userStats: {},
    appVersion: '1.0.0',
    
    // 意见反馈弹窗
    showFeedbackModal: false,
    feedbackContent: '',
    feedbackContact: '',
    isSubmittingFeedback: false,
    
    // 关于我们弹窗
    showAboutModal: false
  },

  onLoad() {
    console.log('个人中心页面加载')
    this.loadUserInfo()
    this.loadUserStats()
  },

  onShow() {
    // 每次显示时刷新用户信息
    this.loadUserInfo()
    this.loadUserStats()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo || {}
    this.setData({ userInfo })
  },

  // 加载用户统计
  loadUserStats() {
    const userId = app.globalData.userInfo?.id
    
    if (!userId) {
      this.setData({
        userStats: {
          totalCount: 0,
          monthCount: 0,
          totalDuration: '0小时'
        }
      })
      return
    }
    
    statisticsAPI.getUserStats(userId)
      .then(res => {
        const stats = res.data
        
        // 处理总时长显示
        const totalDuration = stats.totalDuration ? 
          formatDuration(stats.totalDuration) : '0分钟'
        
        this.setData({
          userStats: {
            ...stats,
            totalDuration
          }
        })
      })
      .catch(error => {
        console.error('加载用户统计失败:', error)
        // 使用默认值
        this.setData({
          userStats: {
            totalCount: 0,
            monthCount: 0,
            totalDuration: '0小时'
          }
        })
      })
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit-profile'
    })
  },

  // 跳转到我的统计
  goToMyStats() {
    wx.navigateTo({
      url: '/pages/statistics/my-statistics'
    })
  },

  // 跳转到历史记录
  goToHistory() {
    wx.navigateTo({
      url: '/pages/statistics/history'
    })
  },

  // 跳转到设置
  goToSettings() {
    wx.navigateTo({
      url: '/pages/profile/settings'
    })
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '在线客服', '客服微信'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 拨打客服电话
            wx.makePhoneCall({
              phoneNumber: '************'
            })
            break
          case 1:
            // 在线客服
            wx.showToast({
              title: '在线客服功能开发中',
              icon: 'none'
            })
            break
          case 2:
            // 客服微信
            wx.showModal({
              title: '客服微信',
              content: '请添加微信号：yunweibao_service',
              confirmText: '复制微信号',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.setClipboardData({
                    data: 'yunweibao_service',
                    success: () => {
                      showSuccess('微信号已复制')
                    }
                  })
                }
              }
            })
            break
        }
      }
    })
  },

  // 显示意见反馈
  showFeedback() {
    this.setData({ 
      showFeedbackModal: true,
      feedbackContent: '',
      feedbackContact: ''
    })
  },

  // 隐藏意见反馈
  hideFeedback() {
    this.setData({ showFeedbackModal: false })
  },

  // 反馈内容输入
  onFeedbackInput(e) {
    this.setData({ feedbackContent: e.detail.value })
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({ feedbackContact: e.detail.value })
  },

  // 提交意见反馈
  submitFeedback() {
    const { feedbackContent, feedbackContact } = this.data
    
    if (!feedbackContent.trim()) {
      showError('请输入反馈内容')
      return
    }
    
    this.setData({ isSubmittingFeedback: true })
    
    const feedbackData = {
      content: feedbackContent.trim(),
      contact: feedbackContact.trim(),
      userId: app.globalData.userInfo?.id,
      userInfo: app.globalData.userInfo,
      submitTime: new Date().toISOString()
    }
    
    // TODO: 调用反馈API
    // 暂时模拟提交
    setTimeout(() => {
      this.setData({ 
        isSubmittingFeedback: false,
        showFeedbackModal: false
      })
      showSuccess('反馈提交成功，感谢您的建议！')
    }, 1500)
  },

  // 显示关于我们
  showAbout() {
    this.setData({ showAboutModal: true })
  },

  // 隐藏关于我们
  hideAbout() {
    this.setData({ showAboutModal: false })
  },

  // 退出登录
  logout() {
    showModal('确认退出', '确定要退出登录吗？')
      .then(() => {
        // 清除本地存储的用户信息
        wx.removeStorageSync('token')
        wx.removeStorageSync('userInfo')
        
        // 清除全局用户信息
        app.globalData.userInfo = null
        app.globalData.token = ''
        
        showSuccess('已退出登录')
        
        // 跳转到登录页面
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/login/login'
          })
        }, 1000)
      })
      .catch(() => {
        // 用户取消退出
      })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '运维宝 - 智能运维管理平台',
      path: '/pages/login/login',
      imageUrl: '/images/share-app.png'
    }
  }
})
