// config/api-config.js
// API接口配置

// 基础配置
const BASE_URL = 'https://api.yunweibao.com' // 生产环境API地址
const DEV_BASE_URL = 'https://dev-api.yunweibao.com' // 开发环境API地址

// 根据环境选择API地址
const getBaseUrl = () => {
  // 可以根据小程序版本或其他条件判断环境
  const accountInfo = wx.getAccountInfoSync()
  const envVersion = accountInfo.miniProgram.envVersion
  
  switch (envVersion) {
    case 'develop':
    case 'trial':
      return DEV_BASE_URL
    case 'release':
    default:
      return BASE_URL
  }
}

// API端点配置
const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    WECHAT_LOGIN: '/auth/wechat-login',           // 微信登录
    BIND_PHONE: '/auth/bind-phone',               // 绑定手机号
    GET_USER_INFO: '/auth/user-info',             // 获取用户信息
    UPDATE_USER_INFO: '/auth/update-user-info',   // 更新用户信息
    REFRESH_TOKEN: '/auth/refresh-token',         // 刷新token
    LOGOUT: '/auth/logout'                        // 退出登录
  },

  // 项目管理
  PROJECT: {
    GET_PROJECT_LIST: '/project/list',            // 获取项目列表
    GET_PROJECT_DETAIL: '/project/detail',        // 获取项目详情
    CREATE_PROJECT: '/project/create',            // 创建项目
    UPDATE_PROJECT: '/project/update',            // 更新项目
    DELETE_PROJECT: '/project/delete'             // 删除项目
  },

  // 站点管理
  SITE: {
    GET_SITE_LIST: '/site/list',                  // 获取站点列表
    SEARCH_SITES: '/site/search',                 // 搜索站点
    GET_SITE_DETAIL: '/site/detail',              // 获取站点详情
    CREATE_SITE: '/site/create',                  // 创建站点
    UPDATE_SITE: '/site/update',                  // 更新站点
    DELETE_SITE: '/site/delete',                  // 删除站点
    GET_NEARBY_SITES: '/site/nearby'              // 获取附近站点
  },

  // 安全交底
  SAFETY: {
    GET_BRIEFING_LIST: '/safety/briefing/list',   // 获取交底列表
    CREATE_BRIEFING: '/safety/briefing/create',   // 创建安全交底
    UPDATE_BRIEFING: '/safety/briefing/update',   // 更新安全交底
    GET_BRIEFING_DETAIL: '/safety/briefing/detail', // 获取交底详情
    COMPLETE_BRIEFING: '/safety/briefing/complete'  // 完成安全交底
  },

  // 运维记录
  MAINTENANCE: {
    GET_RECORD_LIST: '/maintenance/record/list',     // 获取运维记录列表
    CREATE_RECORD: '/maintenance/record/create',     // 创建运维记录
    UPDATE_RECORD: '/maintenance/record/update',     // 更新运维记录
    GET_RECORD_DETAIL: '/maintenance/record/detail', // 获取记录详情
    DELETE_RECORD: '/maintenance/record/delete',     // 删除记录
    CHECKIN: '/maintenance/checkin',                 // 上站打卡
    CHECKOUT: '/maintenance/checkout',               // 离站打卡
    GET_CURRENT_WORK: '/maintenance/current-work'    // 获取当前作业
  },

  // 照片管理
  PHOTO: {
    UPLOAD: '/photo/upload',                      // 上传照片
    BATCH_UPLOAD: '/photo/batch-upload',          // 批量上传照片
    DELETE: '/photo/delete',                      // 删除照片
    GET_PHOTO_LIST: '/photo/list',                // 获取照片列表
    GET_PHOTO_DETAIL: '/photo/detail'             // 获取照片详情
  },

  // AI分析
  AI: {
    FACE_RECOGNITION: '/ai/face-recognition',     // 人脸识别
    ID_CARD_OCR: '/ai/id-card-ocr',              // 身份证识别
    SAFETY_DETECTION: '/ai/safety-detection',     // 安全装备检测
    SCENE_ANALYSIS: '/ai/scene-analysis',         // 现场分析
    WORK_VERIFICATION: '/ai/work-verification'    // 作业验证
  },

  // 统计分析
  STATISTICS: {
    GET_STATS_SUMMARY: '/statistics/summary',           // 获取统计概要
    GET_USER_STATS: '/statistics/user',                 // 获取用户统计
    GET_MAINTENANCE_TREND: '/statistics/maintenance-trend', // 获取运维趋势
    GET_WORK_TYPE_DISTRIBUTION: '/statistics/work-type-distribution', // 获取作业类型分布
    GET_RECORD_SUMMARY: '/statistics/record-summary',   // 获取记录汇总
    GET_CITY_STATS: '/statistics/city',                 // 获取地市统计
    GET_PROJECT_STATS: '/statistics/project'            // 获取项目统计
  },

  // 系统管理
  SYSTEM: {
    GET_CONFIG: '/system/config',                 // 获取系统配置
    CHECK_UPDATE: '/system/check-update',         // 检查更新
    SUBMIT_FEEDBACK: '/system/feedback',          // 提交反馈
    GET_NOTICE_LIST: '/system/notice/list',       // 获取通知列表
    READ_NOTICE: '/system/notice/read'            // 标记通知已读
  }
}

// 请求超时配置
const TIMEOUT_CONFIG = {
  DEFAULT: 10000,        // 默认超时时间 10秒
  UPLOAD: 60000,         // 上传超时时间 60秒
  AI_ANALYSIS: 30000     // AI分析超时时间 30秒
}

// 重试配置
const RETRY_CONFIG = {
  MAX_RETRY_COUNT: 3,    // 最大重试次数
  RETRY_DELAY: 1000      // 重试延迟时间
}

// 缓存配置
const CACHE_CONFIG = {
  USER_INFO: 'cache_user_info',
  PROJECT_LIST: 'cache_project_list',
  SITE_LIST: 'cache_site_list',
  STATS_SUMMARY: 'cache_stats_summary',
  CACHE_EXPIRE_TIME: 5 * 60 * 1000  // 缓存过期时间 5分钟
}

// 导出配置
module.exports = {
  getBaseUrl,
  API_ENDPOINTS,
  TIMEOUT_CONFIG,
  RETRY_CONFIG,
  CACHE_CONFIG
}
