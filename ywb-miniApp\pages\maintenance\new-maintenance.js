// pages/maintenance/new-maintenance.js
const app = getApp()
const { projectAPI, safetyAPI, maintenanceAPI } = require('../../utils/api')
const { formatTime, formatDuration, previewImage } = require('../../utils/util')

Page({
  data: {
    projectId: '',
    projectInfo: {},
    todayBriefing: null,
    todayRecords: [],
    loading: true
  },

  onLoad(options) {
    console.log('新增运维页面加载', options)
    
    const { projectId, projectName } = options
    
    if (!projectId) {
      wx.showToast({
        title: '项目参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }
    
    this.setData({
      projectId,
      projectInfo: {
        projectName: decodeURIComponent(projectName || '')
      }
    })
    
    this.loadPageData()
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (this.data.projectId) {
      this.loadTodayData()
    }
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载页面数据
  loadPageData() {
    this.setData({ loading: true })
    
    return Promise.all([
      this.loadProjectInfo(),
      this.loadTodayData()
    ]).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 加载项目信息
  loadProjectInfo() {
    return projectAPI.getProjectDetail(this.data.projectId)
      .then(res => {
        this.setData({
          projectInfo: res.data
        })
      })
      .catch(error => {
        console.error('加载项目信息失败:', error)
        wx.showToast({
          title: '加载项目信息失败',
          icon: 'none'
        })
      })
  },

  // 加载今日数据
  loadTodayData() {
    return Promise.all([
      this.loadTodayBriefing(),
      this.loadTodayRecords()
    ])
  },

  // 加载今日交底信息
  loadTodayBriefing() {
    const userId = app.globalData.userInfo.id
    const today = formatTime(new Date(), 'YYYY-MM-DD')
    
    return safetyAPI.getSafetyBriefingList({
      userId,
      projectId: this.data.projectId,
      briefingDate: today
    }).then(res => {
      const briefingList = res.data.list || []
      
      if (briefingList.length > 0) {
        const briefing = briefingList[0]
        
        // 处理参与人员显示
        let participantsText = ''
        if (briefing.participants && briefing.participants.length > 0) {
          participantsText = briefing.participants.map(p => p.name).join('、')
        }
        
        this.setData({
          todayBriefing: {
            ...briefing,
            briefingTime: formatTime(briefing.createTime, 'HH:mm'),
            participantsText
          }
        })
      } else {
        this.setData({ todayBriefing: null })
      }
    }).catch(error => {
      console.error('加载今日交底失败:', error)
      this.setData({ todayBriefing: null })
    })
  },

  // 加载今日运维记录
  loadTodayRecords() {
    const userId = app.globalData.userInfo.id
    const today = formatTime(new Date(), 'YYYY-MM-DD')
    
    return maintenanceAPI.getMaintenanceRecordList({
      userId,
      projectId: this.data.projectId,
      workDate: today
    }).then(res => {
      const records = res.data.list || []
      
      // 处理记录数据
      const processedRecords = records.map(record => {
        let statusText = ''
        switch (record.status) {
          case 1:
            statusText = '进行中'
            break
          case 2:
            statusText = '已完成'
            break
          case 3:
            statusText = '异常'
            break
          default:
            statusText = '未知'
        }
        
        return {
          ...record,
          statusText,
          checkinTime: record.checkinTime ? formatTime(record.checkinTime, 'HH:mm') : null,
          checkoutTime: record.checkoutTime ? formatTime(record.checkoutTime, 'HH:mm') : null,
          workDurationText: record.workDuration ? formatDuration(record.workDuration) : null
        }
      })
      
      this.setData({ todayRecords: processedRecords })
    }).catch(error => {
      console.error('加载今日运维记录失败:', error)
      this.setData({ todayRecords: [] })
    })
  },

  // 查看项目详情
  viewProjectDetail() {
    wx.navigateTo({
      url: `/pages/project/project-detail?id=${this.data.projectId}`
    })
  },

  // 预览交底照片
  previewBriefingPhoto() {
    const { todayBriefing } = this.data
    if (todayBriefing && todayBriefing.photoUrl) {
      previewImage([todayBriefing.photoUrl])
    }
  },

  // 去安全交底
  goToBriefing() {
    wx.navigateTo({
      url: `/pages/maintenance/safety-briefing?projectId=${this.data.projectId}`
    })
  },

  // 开始运维（上站打卡）
  startMaintenance() {
    const { todayBriefing } = this.data
    
    if (!todayBriefing) {
      wx.showToast({
        title: '请先完成安全交底',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/maintenance/checkin-step1?projectId=${this.data.projectId}&briefingId=${todayBriefing.id}`
    })
  },

  // 去离站打卡
  goToCheckout(e) {
    const record = e.currentTarget.dataset.record
    
    wx.navigateTo({
      url: `/pages/maintenance/checkout?recordId=${record.id}`
    })
  },

  // 查看记录详情
  viewRecordDetail(e) {
    const record = e.currentTarget.dataset.record
    
    wx.navigateTo({
      url: `/pages/maintenance/record-detail?recordId=${record.id}`
    })
  },

  // 处理异常记录
  handleAbnormal(e) {
    const record = e.currentTarget.dataset.record
    
    wx.showActionSheet({
      itemList: ['重新打卡', '联系管理员', '查看详情'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 重新打卡
            this.retryMaintenance(record)
            break
          case 1:
            // 联系管理员
            this.contactAdmin()
            break
          case 2:
            // 查看详情
            this.viewRecordDetail(e)
            break
        }
      }
    })
  },

  // 重新打卡
  retryMaintenance(record) {
    wx.showModal({
      title: '重新打卡',
      content: '确定要重新开始这个站点的运维工作吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: `/pages/maintenance/checkin-step1?projectId=${this.data.projectId}&siteId=${record.siteId}&retryRecordId=${record.id}`
          })
        }
      }
    })
  },

  // 联系管理员
  contactAdmin() {
    wx.showModal({
      title: '联系管理员',
      content: '请联系项目管理员处理异常情况\n\n联系电话：************',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567'
          })
        }
      }
    })
  },

  // 去历史运维
  goToHistory() {
    wx.switchTab({
      url: '/pages/statistics/statistics'
    })
  },

  // 去运维统计
  goToStatistics() {
    wx.navigateTo({
      url: '/pages/statistics/my-statistics'
    })
  },

  // 去站点管理
  goToSiteManage() {
    wx.navigateTo({
      url: `/pages/site/site-selection?projectId=${this.data.projectId}`
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `${this.data.projectInfo.projectName} - 运维管理`,
      path: `/pages/maintenance/new-maintenance?projectId=${this.data.projectId}&projectName=${encodeURIComponent(this.data.projectInfo.projectName)}`,
      imageUrl: '/images/share-maintenance.png'
    }
  }
})
