// utils/api.js
// API接口封装

const app = getApp()

// 基础请求方法
const request = (options) => {
  return new Promise((resolve, reject) => {
    const { url, method = 'GET', data = {}, header = {} } = options
    
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    wx.request({
      url: `${app.globalData.baseUrl}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${app.globalData.token || ''}`,
        ...header
      },
      success: (res) => {
        wx.hideLoading()
        
        if (res.statusCode === 200) {
          if (res.data.success) {
            resolve(res.data)
          } else {
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // token过期，重新登录
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
          app.logout()
          reject(res.data)
        } else {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
          reject(res.data)
        }
      },
      fail: (error) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// 文件上传方法
const uploadFile = (filePath, fileName = 'file') => {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })

    wx.uploadFile({
      url: `${app.globalData.baseUrl}/api/file/upload`,
      filePath,
      name: fileName,
      header: {
        'Authorization': `Bearer ${app.globalData.token || ''}`
      },
      success: (res) => {
        wx.hideLoading()
        
        if (res.statusCode === 200) {
          const data = JSON.parse(res.data)
          if (data.success) {
            resolve(data)
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            })
            reject(data)
          }
        } else {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (error) => {
        wx.hideLoading()
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// 用户认证相关API
const authAPI = {
  // 微信登录
  wechatLogin: (code) => {
    return request({
      url: '/api/auth/wechat-login',
      method: 'POST',
      data: { code }
    })
  },

  // 绑定手机号
  bindPhone: (phoneNumber, code) => {
    return request({
      url: '/api/auth/bind-phone',
      method: 'POST',
      data: { phoneNumber, code }
    })
  },

  // 获取用户信息
  getUserInfo: () => {
    return request({
      url: '/api/auth/user-info',
      method: 'GET'
    })
  },

  // 刷新token
  refreshToken: (refreshToken) => {
    return request({
      url: '/api/auth/refresh-token',
      method: 'POST',
      data: { refreshToken }
    })
  }
}

// 项目管理相关API
const projectAPI = {
  // 获取项目列表
  getProjectList: (params = {}) => {
    return request({
      url: '/api/project/list',
      method: 'GET',
      data: params
    })
  },

  // 获取项目详情
  getProjectDetail: (projectId) => {
    return request({
      url: `/api/project/${projectId}`,
      method: 'GET'
    })
  },

  // 获取用户项目统计
  getUserProjectStats: (userId) => {
    return request({
      url: `/api/project/user-stats/${userId}`,
      method: 'GET'
    })
  }
}

// 站点管理相关API
const siteAPI = {
  // 获取站点列表
  getSiteList: (params = {}) => {
    return request({
      url: '/api/site/list',
      method: 'GET',
      data: params
    })
  },

  // 搜索站点
  searchSites: (keyword, projectId) => {
    return request({
      url: '/api/site/search',
      method: 'GET',
      data: { keyword, projectId }
    })
  },

  // 新增站点
  createSite: (siteData) => {
    return request({
      url: '/api/site/create',
      method: 'POST',
      data: siteData
    })
  },

  // 更新站点
  updateSite: (siteId, siteData) => {
    return request({
      url: `/api/site/${siteId}`,
      method: 'PUT',
      data: siteData
    })
  },

  // 删除站点
  deleteSite: (siteId) => {
    return request({
      url: `/api/site/${siteId}`,
      method: 'DELETE'
    })
  }
}

// 安全交底相关API
const safetyAPI = {
  // 创建安全交底
  createSafetyBriefing: (briefingData) => {
    return request({
      url: '/api/safety/briefing/create',
      method: 'POST',
      data: briefingData
    })
  },

  // 获取安全交底列表
  getSafetyBriefingList: (params = {}) => {
    return request({
      url: '/api/safety/briefing/list',
      method: 'GET',
      data: params
    })
  },

  // 获取安全交底详情
  getSafetyBriefingDetail: (briefingId) => {
    return request({
      url: `/api/safety/briefing/${briefingId}`,
      method: 'GET'
    })
  },

  // 检查用户今日是否已交底
  checkTodayBriefing: (userId, projectId) => {
    return request({
      url: '/api/safety/briefing/check-today',
      method: 'GET',
      data: { userId, projectId }
    })
  }
}

// 运维记录相关API
const maintenanceAPI = {
  // 创建运维记录
  createMaintenanceRecord: (recordData) => {
    return request({
      url: '/api/maintenance/record/create',
      method: 'POST',
      data: recordData
    })
  },

  // 上站打卡
  checkin: (recordId, checkinData) => {
    return request({
      url: `/api/maintenance/record/${recordId}/checkin`,
      method: 'POST',
      data: checkinData
    })
  },

  // 离站打卡
  checkout: (recordId, checkoutData) => {
    return request({
      url: `/api/maintenance/record/${recordId}/checkout`,
      method: 'POST',
      data: checkoutData
    })
  },

  // 获取运维记录列表
  getMaintenanceRecordList: (params = {}) => {
    return request({
      url: '/api/maintenance/record/list',
      method: 'GET',
      data: params
    })
  },

  // 获取运维记录详情
  getMaintenanceRecordDetail: (recordId) => {
    return request({
      url: `/api/maintenance/record/${recordId}`,
      method: 'GET'
    })
  },

  // 获取用户运维统计
  getUserMaintenanceStats: (params = {}) => {
    return request({
      url: '/api/maintenance/record/user-stats',
      method: 'GET',
      data: params
    })
  }
}

// 照片管理相关API
const photoAPI = {
  // 上传照片
  uploadPhoto: (filePath, businessType, businessId) => {
    return uploadFile(filePath, 'photo').then(res => {
      // 上传成功后保存照片记录
      return request({
        url: '/api/photo/create',
        method: 'POST',
        data: {
          businessType,
          businessId,
          photoUrl: res.data.url,
          photoName: res.data.name,
          photoSize: res.data.size,
          photoType: res.data.type
        }
      })
    })
  },

  // 获取照片列表
  getPhotoList: (businessType, businessId) => {
    return request({
      url: '/api/photo/list',
      method: 'GET',
      data: { businessType, businessId }
    })
  },

  // 删除照片
  deletePhoto: (photoId) => {
    return request({
      url: `/api/photo/${photoId}`,
      method: 'DELETE'
    })
  }
}

// AI分析相关API
const aiAPI = {
  // 人脸识别
  faceRecognition: (photoId) => {
    return request({
      url: '/api/ai/face-recognition',
      method: 'POST',
      data: { photoId }
    })
  },

  // 身份证识别
  idCardRecognition: (photoId) => {
    return request({
      url: '/api/ai/id-card-recognition',
      method: 'POST',
      data: { photoId }
    })
  },

  // 防护用品检测
  safetyGearDetection: (photoId) => {
    return request({
      url: '/api/ai/safety-gear-detection',
      method: 'POST',
      data: { photoId }
    })
  },

  // 场景识别
  sceneRecognition: (photoId) => {
    return request({
      url: '/api/ai/scene-recognition',
      method: 'POST',
      data: { photoId }
    })
  },

  // 获取AI分析结果
  getAnalysisResult: (photoId) => {
    return request({
      url: `/api/ai/analysis-result/${photoId}`,
      method: 'GET'
    })
  }
}

// 统计分析相关API
const statisticsAPI = {
  // 获取历史运维记录
  getHistoryRecords: (params = {}) => {
    return request({
      url: '/api/statistics/history-records',
      method: 'GET',
      data: params
    })
  },

  // 获取个人统计数据
  getPersonalStats: (params = {}) => {
    return request({
      url: '/api/statistics/personal-stats',
      method: 'GET',
      data: params
    })
  },

  // 获取地市统计数据
  getCityStats: (params = {}) => {
    return request({
      url: '/api/statistics/city-stats',
      method: 'GET',
      data: params
    })
  },

  // 获取项目统计数据
  getProjectStats: (params = {}) => {
    return request({
      url: '/api/statistics/project-stats',
      method: 'GET',
      data: params
    })
  }
}

module.exports = {
  request,
  uploadFile,
  authAPI,
  projectAPI,
  siteAPI,
  safetyAPI,
  maintenanceAPI,
  photoAPI,
  aiAPI,
  statisticsAPI
}
