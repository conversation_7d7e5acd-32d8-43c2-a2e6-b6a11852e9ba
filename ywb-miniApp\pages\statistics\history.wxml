<!--pages/statistics/history.wxml-->
<view class="history-container">
  <!-- 筛选栏 -->
  <view class="filter-section">
    <view class="filter-row">
      <picker 
        mode="date" 
        value="{{startDate}}" 
        end="{{endDate}}"
        bindchange="onStartDateChange"
      >
        <view class="date-picker">
          <text class="picker-label">开始日期</text>
          <text class="picker-value">{{startDate || '请选择'}}</text>
        </view>
      </picker>
      
      <text class="date-separator">至</text>
      
      <picker 
        mode="date" 
        value="{{endDate}}" 
        start="{{startDate}}"
        bindchange="onEndDateChange"
      >
        <view class="date-picker">
          <text class="picker-label">结束日期</text>
          <text class="picker-value">{{endDate || '请选择'}}</text>
        </view>
      </picker>
    </view>
    
    <view class="filter-row">
      <picker 
        mode="selector" 
        range="{{statusList}}" 
        range-key="name"
        value="{{statusIndex}}" 
        bindchange="onStatusChange"
      >
        <view class="status-picker">
          <text class="picker-label">状态筛选</text>
          <text class="picker-value">{{statusIndex === -1 ? '全部状态' : statusList[statusIndex].name}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
      
      <button class="search-btn" bindtap="searchRecords">搜索</button>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="summary-section" wx:if="{{searchSummary}}">
    <view class="summary-item">
      <text class="summary-number">{{searchSummary.totalCount || 0}}</text>
      <text class="summary-label">总记录数</text>
    </view>
    <view class="summary-item">
      <text class="summary-number">{{searchSummary.completedCount || 0}}</text>
      <text class="summary-label">已完成</text>
    </view>
    <view class="summary-item">
      <text class="summary-number">{{searchSummary.totalDuration || '0小时'}}</text>
      <text class="summary-label">总时长</text>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="records-section">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载记录...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{recordList.length === 0 && !loading}}">
      <text class="empty-icon">📋</text>
      <text class="empty-text">{{hasSearched ? '未找到符合条件的记录' : '暂无运维记录'}}</text>
    </view>

    <!-- 记录列表 -->
    <scroll-view 
      class="records-scroll" 
      scroll-y="true" 
      wx:else
      bindscrolltolower="loadMoreRecords"
      lower-threshold="100"
    >
      <view class="records-list">
        <view 
          class="record-item"
          wx:for="{{recordList}}" 
          wx:key="id"
          bindtap="viewRecordDetail"
          data-record="{{item}}"
        >
          <view class="record-header">
            <text class="record-site">{{item.siteName}}</text>
            <view class="record-status {{item.status === 2 ? 'completed' : item.status === 1 ? 'ongoing' : 'exception'}}">
              <text class="status-text">{{item.statusText}}</text>
            </view>
          </view>
          
          <view class="record-content">
            <view class="record-info">
              <text class="info-item">{{item.workTypeText}}</text>
              <text class="info-separator">•</text>
              <text class="info-item">{{item.workDate}}</text>
              <text class="info-separator">•</text>
              <text class="info-item">{{item.checkinTime}}</text>
            </view>
            
            <view class="record-details">
              <view class="detail-item" wx:if="{{item.workDurationText}}">
                <text class="detail-label">作业时长：</text>
                <text class="detail-value">{{item.workDurationText}}</text>
              </view>
              <view class="detail-item" wx:if="{{item.safetyFactorsText}}">
                <text class="detail-label">安全要素：</text>
                <text class="detail-value">{{item.safetyFactorsText}}</text>
              </view>
            </view>
          </view>
          
          <view class="record-footer" wx:if="{{item.projectName}}">
            <text class="project-name">{{item.projectName}}</text>
            <text class="record-code">{{item.recordCode}}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <view class="load-more-spinner" wx:if="{{loadingMore}}"></view>
        <text class="load-more-text">{{loadingMore ? '加载中...' : '上拉加载更多'}}</text>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && recordList.length > 0}}">
        <text class="no-more-text">没有更多记录了</text>
      </view>
    </scroll-view>
  </view>
</view>
