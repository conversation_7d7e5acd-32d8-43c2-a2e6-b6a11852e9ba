<!--pages/statistics/statistics.wxml-->
<view class="statistics-container">
  <!-- 顶部统计卡片 -->
  <view class="stats-cards">
    <view class="stats-card">
      <text class="card-number">{{todayStats.count || 0}}</text>
      <text class="card-label">今日运维</text>
      <text class="card-unit">次</text>
    </view>
    <view class="stats-card">
      <text class="card-number">{{monthStats.count || 0}}</text>
      <text class="card-label">本月运维</text>
      <text class="card-unit">次</text>
    </view>
    <view class="stats-card">
      <text class="card-number">{{totalStats.count || 0}}</text>
      <text class="card-label">累计运维</text>
      <text class="card-unit">次</text>
    </view>
  </view>

  <!-- 快速入口 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="goToHistory">
      <view class="action-icon history">
        <text class="icon-text">📋</text>
      </view>
      <text class="action-label">历史运维</text>
    </view>
    <view class="action-item" bindtap="goToMyStats">
      <view class="action-icon personal">
        <text class="icon-text">👤</text>
      </view>
      <text class="action-label">我的统计</text>
    </view>
    <view class="action-item" bindtap="goToCityStats">
      <view class="action-icon city">
        <text class="icon-text">🏙️</text>
      </view>
      <text class="action-label">地市统计</text>
    </view>
    <view class="action-item" bindtap="goToProjectStats">
      <view class="action-icon project">
        <text class="icon-text">📊</text>
      </view>
      <text class="action-label">项目统计</text>
    </view>
  </view>

  <!-- 最近运维记录 -->
  <view class="recent-section">
    <view class="section-header">
      <text class="section-title">最近运维</text>
      <text class="section-more" bindtap="goToHistory">查看全部 ></text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{recentRecords.length === 0}}">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无运维记录</text>
    </view>

    <!-- 记录列表 -->
    <view class="records-list" wx:else>
      <view 
        class="record-item"
        wx:for="{{recentRecords}}" 
        wx:key="id"
        bindtap="viewRecordDetail"
        data-record="{{item}}"
      >
        <view class="record-header">
          <text class="record-site">{{item.siteName}}</text>
          <view class="record-status {{item.status === 2 ? 'completed' : item.status === 1 ? 'ongoing' : 'exception'}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>
        
        <view class="record-content">
          <view class="record-info">
            <text class="info-item">{{item.workTypeText}}</text>
            <text class="info-separator">•</text>
            <text class="info-item">{{item.workDate}}</text>
          </view>
          
          <view class="record-time">
            <text class="time-label">作业时长：</text>
            <text class="time-value">{{item.workDurationText || '进行中'}}</text>
          </view>
        </view>
        
        <view class="record-footer" wx:if="{{item.projectName}}">
          <text class="project-name">{{item.projectName}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计图表 -->
  <view class="chart-section">
    <view class="section-header">
      <text class="section-title">运维趋势</text>
      <view class="chart-tabs">
        <text 
          class="tab-item {{chartType === 'week' ? 'active' : ''}}"
          bindtap="switchChartType"
          data-type="week"
        >
          近7天
        </text>
        <text 
          class="tab-item {{chartType === 'month' ? 'active' : ''}}"
          bindtap="switchChartType"
          data-type="month"
        >
          近30天
        </text>
      </view>
    </view>
    
    <!-- 简单的柱状图展示 -->
    <view class="chart-container">
      <view class="chart-bars">
        <view 
          class="chart-bar"
          wx:for="{{chartData}}" 
          wx:key="date"
        >
          <view 
            class="bar-fill"
            style="height: {{item.percentage}}%"
          ></view>
          <text class="bar-value">{{item.count}}</text>
          <text class="bar-label">{{item.label}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 运维类型分布 -->
  <view class="type-distribution">
    <view class="section-header">
      <text class="section-title">运维类型分布</text>
    </view>
    
    <view class="distribution-list">
      <view 
        class="distribution-item"
        wx:for="{{typeDistribution}}" 
        wx:key="type"
      >
        <view class="type-info">
          <text class="type-name">{{item.typeName}}</text>
          <text class="type-count">{{item.count}}次</text>
        </view>
        <view class="type-progress">
          <view 
            class="progress-fill"
            style="width: {{item.percentage}}%"
          ></view>
        </view>
        <text class="type-percentage">{{item.percentage}}%</text>
      </view>
    </view>
  </view>
</view>
