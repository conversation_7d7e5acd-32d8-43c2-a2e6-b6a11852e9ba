// pages/statistics/statistics.js
const app = getApp()
const { statisticsAPI, maintenanceAPI } = require('../../utils/api')
const { formatTime, formatDuration, showError } = require('../../utils/util')

Page({
  data: {
    // 统计数据
    todayStats: {},
    monthStats: {},
    totalStats: {},
    
    // 最近记录
    recentRecords: [],
    loading: true,
    
    // 图表数据
    chartType: 'week', // week, month
    chartData: [],
    
    // 类型分布
    typeDistribution: []
  },

  onLoad() {
    console.log('统计页面加载')
    this.loadPageData()
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadPageData()
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载页面数据
  loadPageData() {
    this.setData({ loading: true })
    
    return Promise.all([
      this.loadStatsSummary(),
      this.loadRecentRecords(),
      this.loadChartData(),
      this.loadTypeDistribution()
    ]).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 加载统计概要
  loadStatsSummary() {
    return statisticsAPI.getStatsSummary()
      .then(res => {
        const { todayStats, monthStats, totalStats } = res.data
        
        this.setData({
          todayStats,
          monthStats,
          totalStats
        })
      })
      .catch(error => {
        console.error('加载统计概要失败:', error)
        // 使用模拟数据
        this.setData({
          todayStats: { count: 3 },
          monthStats: { count: 28 },
          totalStats: { count: 156 }
        })
      })
  },

  // 加载最近记录
  loadRecentRecords() {
    const userId = app.globalData.userInfo.id
    
    return maintenanceAPI.getMaintenanceRecordList({
      userId,
      page: 1,
      pageSize: 5,
      orderBy: 'createTime',
      orderType: 'desc'
    }).then(res => {
      const records = res.data.list || []
      
      // 处理记录数据
      const processedRecords = records.map(record => ({
        ...record,
        statusText: this.getStatusText(record.status),
        workTypeText: this.getWorkTypeText(record.workType),
        workDate: formatTime(record.workDate, 'MM-DD'),
        workDurationText: record.workDuration ? formatDuration(record.workDuration) : null
      }))
      
      this.setData({ recentRecords: processedRecords })
    }).catch(error => {
      console.error('加载最近记录失败:', error)
      this.setData({ recentRecords: [] })
    })
  },

  // 加载图表数据
  loadChartData() {
    const { chartType } = this.data
    const days = chartType === 'week' ? 7 : 30
    
    return statisticsAPI.getMaintenanceTrend({ days })
      .then(res => {
        const trendData = res.data.list || []
        
        // 处理图表数据
        const maxCount = Math.max(...trendData.map(item => item.count), 1)
        
        const chartData = trendData.map(item => ({
          ...item,
          label: chartType === 'week' ? 
            formatTime(item.date, 'MM-DD').substring(3) : 
            formatTime(item.date, 'DD'),
          percentage: Math.max((item.count / maxCount) * 100, 5) // 最小5%高度
        }))
        
        this.setData({ chartData })
      })
      .catch(error => {
        console.error('加载图表数据失败:', error)
        // 使用模拟数据
        this.generateMockChartData()
      })
  },

  // 生成模拟图表数据
  generateMockChartData() {
    const { chartType } = this.data
    const days = chartType === 'week' ? 7 : 30
    const mockData = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      
      const count = Math.floor(Math.random() * 8) // 0-7次
      mockData.push({
        date: formatTime(date, 'YYYY-MM-DD'),
        count,
        label: chartType === 'week' ? 
          formatTime(date, 'MM-DD').substring(3) : 
          formatTime(date, 'DD'),
        percentage: Math.max((count / 8) * 100, 5)
      })
    }
    
    this.setData({ chartData: mockData })
  },

  // 加载类型分布
  loadTypeDistribution() {
    return statisticsAPI.getWorkTypeDistribution()
      .then(res => {
        const distribution = res.data.list || []
        
        // 计算总数和百分比
        const totalCount = distribution.reduce((sum, item) => sum + item.count, 0)
        
        const processedDistribution = distribution.map(item => ({
          ...item,
          typeName: this.getWorkTypeText(item.type),
          percentage: totalCount > 0 ? Math.round((item.count / totalCount) * 100) : 0
        }))
        
        this.setData({ typeDistribution: processedDistribution })
      })
      .catch(error => {
        console.error('加载类型分布失败:', error)
        // 使用模拟数据
        this.setData({
          typeDistribution: [
            { type: 'repair', typeName: '配套维修', count: 45, percentage: 65 },
            { type: 'tower', typeName: '杆塔维修', count: 24, percentage: 35 }
          ]
        })
      })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      1: '进行中',
      2: '已完成',
      3: '异常'
    }
    return statusMap[status] || '未知'
  },

  // 获取作业类型文本
  getWorkTypeText(workType) {
    const typeMap = {
      'repair': '配套维修',
      'tower': '杆塔维修'
    }
    return typeMap[workType] || workType
  },

  // 切换图表类型
  switchChartType(e) {
    const type = e.currentTarget.dataset.type
    
    if (type === this.data.chartType) return
    
    this.setData({ chartType: type })
    this.loadChartData()
  },

  // 查看记录详情
  viewRecordDetail(e) {
    const record = e.currentTarget.dataset.record
    
    wx.navigateTo({
      url: `/pages/maintenance/record-detail?recordId=${record.id}`
    })
  },

  // 跳转到历史运维
  goToHistory() {
    wx.navigateTo({
      url: '/pages/statistics/history'
    })
  },

  // 跳转到我的统计
  goToMyStats() {
    wx.navigateTo({
      url: '/pages/statistics/my-statistics'
    })
  },

  // 跳转到地市统计
  goToCityStats() {
    wx.navigateTo({
      url: '/pages/statistics/city-statistics'
    })
  },

  // 跳转到项目统计
  goToProjectStats() {
    wx.navigateTo({
      url: '/pages/statistics/project-statistics'
    })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '运维统计 - 运维宝',
      path: '/pages/statistics/statistics',
      imageUrl: '/images/share-statistics.png'
    }
  }
})
