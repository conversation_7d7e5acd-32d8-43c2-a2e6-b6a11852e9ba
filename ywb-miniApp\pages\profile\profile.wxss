/* pages/profile/profile.wxss */
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(25, 118, 210, 0.3);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.user-phone,
.user-id {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-actions {
  display: flex;
  flex-direction: column;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #fff;
}

.edit-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 统计数据 */
.stats-section {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.stats-item:active {
  transform: scale(0.95);
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #1976d2;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.menu-group {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f9fa;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.menu-icon.stats {
  background: #e3f2fd;
}

.menu-icon.history {
  background: #e8f5e8;
}

.menu-icon.settings {
  background: #fff3e0;
}

.menu-icon.service {
  background: #f3e5f5;
}

.menu-icon.feedback {
  background: #e0f2f1;
}

.menu-icon.about {
  background: #fce4ec;
}

.menu-label {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  margin-bottom: 32rpx;
}

.logout-btn {
  width: 100%;
  background: #fff;
  border: 2rpx solid #f44336;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 28rpx;
  color: #f44336;
  font-weight: 500;
}

.logout-btn:active {
  background: #ffebee;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 16rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}

/* 弹窗样式 */
.feedback-modal,
.about-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: #fff;
  border-radius: 16rpx;
  width: 640rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 意见反馈 */
.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.feedback-textarea:focus {
  border-color: #1976d2;
  outline: none;
}

.feedback-counter {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.contact-input {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background: #fff;
  color: #333;
}

.contact-input:focus {
  border-color: #1976d2;
  outline: none;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e0e0e0;
}

.submit-btn {
  background: #1976d2;
  color: #fff;
}

.submit-btn:active {
  background: #1565c0;
}

.submit-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

/* 关于我们 */
.about-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  text-align: center;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background: #f0f0f0;
}

.logo-image {
  width: 100%;
  height: 100%;
}

.app-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.app-version {
  font-size: 24rpx;
  color: #666;
}

.app-description {
  padding: 24rpx 0;
}

.desc-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.company-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.company-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.copyright {
  font-size: 22rpx;
  color: #999;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .user-info {
    align-items: center;
  }
  
  .stats-section {
    flex-direction: column;
    gap: 24rpx;
  }
  
  .stats-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
