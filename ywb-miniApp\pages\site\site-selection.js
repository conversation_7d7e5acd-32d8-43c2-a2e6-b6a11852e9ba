// pages/site/site-selection.js
const app = getApp()
const { siteAPI } = require('../../utils/api')
const { showError, showSuccess, debounce } = require('../../utils/util')

Page({
  data: {
    projectId: '',
    selectMode: false, // 是否为选择模式
    selectedSiteId: '',
    
    // 搜索和筛选
    searchKeyword: '',
    filterType: '', // '', 'room', 'tower', 'substation', 'line'
    
    // 列表数据
    siteList: [],
    loading: true,
    loadingMore: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 20
  },

  onLoad(options) {
    console.log('站点选择页面加载', options)
    
    const { projectId, selectMode } = options
    
    if (!projectId) {
      showError('项目参数错误')
      wx.navigateBack()
      return
    }
    
    this.setData({
      projectId,
      selectMode: selectMode === 'true'
    })
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.selectMode ? '选择站点' : '站点管理'
    })
    
    this.loadSiteList()
  },

  onPullDownRefresh() {
    this.refreshSiteList()
  },

  // 搜索输入
  onSearchInput: debounce(function(e) {
    this.setData({ searchKeyword: e.detail.value })
    this.refreshSiteList()
  }, 500),

  // 清除搜索
  clearSearch() {
    this.setData({ searchKeyword: '' })
    this.refreshSiteList()
  },

  // 执行搜索
  performSearch() {
    this.refreshSiteList()
  },

  // 选择筛选条件
  selectFilter(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ filterType: type })
    this.refreshSiteList()
  },

  // 刷新站点列表
  refreshSiteList() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      siteList: []
    })
    this.loadSiteList()
  },

  // 加载站点列表
  loadSiteList() {
    if (this.data.loading && this.data.currentPage > 1) return
    
    this.setData({ 
      loading: this.data.currentPage === 1,
      loadingMore: this.data.currentPage > 1
    })
    
    const {
      projectId,
      searchKeyword,
      filterType,
      currentPage,
      pageSize
    } = this.data
    
    const params = {
      projectId,
      keyword: searchKeyword.trim(),
      siteType: filterType,
      page: currentPage,
      pageSize
    }
    
    siteAPI.getSiteList(params)
      .then(res => {
        const { list, total, hasMore } = res.data
        
        // 处理站点数据
        const processedList = list.map(site => ({
          ...site,
          siteTypeText: this.getSiteTypeText(site.siteType),
          stats: site.stats || null
        }))
        
        if (currentPage === 1) {
          this.setData({
            siteList: processedList,
            hasMore
          })
        } else {
          this.setData({
            siteList: [...this.data.siteList, ...processedList],
            hasMore
          })
        }
        
        this.setData({ currentPage: currentPage + 1 })
      })
      .catch(error => {
        console.error('加载站点列表失败:', error)
        showError('加载站点列表失败')
      })
      .finally(() => {
        this.setData({
          loading: false,
          loadingMore: false
        })
        
        if (this.data.currentPage === 2) {
          wx.stopPullDownRefresh()
        }
      })
  },

  // 加载更多站点
  loadMoreSites() {
    if (!this.data.hasMore || this.data.loadingMore) return
    this.loadSiteList()
  },

  // 获取站点类型文本
  getSiteTypeText(siteType) {
    const typeMap = {
      'room': '机房',
      'tower': '杆塔',
      'substation': '变电站',
      'line': '线路'
    }
    return typeMap[siteType] || '未知'
  },

  // 选择站点
  selectSite(e) {
    if (!this.data.selectMode) return
    
    const site = e.currentTarget.dataset.site
    
    this.setData({
      selectedSiteId: site.id === this.data.selectedSiteId ? '' : site.id
    })
  },

  // 查看站点详情
  viewSiteDetail(e) {
    if (this.data.selectMode) return
    
    const site = e.currentTarget.dataset.site
    
    wx.navigateTo({
      url: `/pages/site/site-detail?siteId=${site.id}`
    })
  },

  // 确认选择
  confirmSelection() {
    if (!this.data.selectedSiteId) return
    
    const selectedSite = this.data.siteList.find(site => site.id === this.data.selectedSiteId)
    
    if (!selectedSite) {
      showError('请选择一个站点')
      return
    }
    
    // 将选中的站点数据存储到本地，供调用页面使用
    wx.setStorageSync('selectedSiteForCheckin', selectedSite)
    
    showSuccess('站点选择成功')
    
    // 返回上一页
    setTimeout(() => {
      wx.navigateBack()
    }, 1000)
  },

  // 新增站点
  addNewSite() {
    const returnPage = this.data.selectMode ? 'checkin' : 'site-selection'
    
    wx.navigateTo({
      url: `/pages/site/new-site?projectId=${this.data.projectId}&returnPage=${returnPage}`
    })
  },

  // 页面显示时的处理
  onShow() {
    // 检查是否从新增站点页面返回
    const newSiteAdded = wx.getStorageSync('newSiteAdded')
    if (newSiteAdded) {
      wx.removeStorageSync('newSiteAdded')
      // 刷新列表
      this.refreshSiteList()
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '站点管理 - 运维宝',
      path: `/pages/site/site-selection?projectId=${this.data.projectId}`,
      imageUrl: '/images/share-site-management.png'
    }
  }
})
