import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import menuProps from"../dropdown-menu/props";import{getRect}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-dropdown-item`;let DropdownMenuItem=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`,`${prefix}-class-content`,`${prefix}-class-column`,`${prefix}-class-column-item`,`${prefix}-class-column-item-label`,`${prefix}-class-footer`],this.properties=props,this.data={prefix:prefix,classPrefix:name,show:!1,top:0,maskHeight:0,initValue:null,hasChanged:!1,duration:menuProps.duration.value,zIndex:menuProps.zIndex.value,overlay:menuProps.showOverlay.value,labelAlias:"label",valueAlias:"value",computedLabel:"",firstCheckedValue:""},this.relations={"../dropdown-menu/dropdown-menu":{type:"parent",linked(e){const{zIndex:t,duration:a,showOverlay:s}=e.properties;this.setData({zIndex:t,duration:a,showOverlay:s})}}},this.controlledProps=[{key:"value",event:"change"}],this.observers={keys(e){this.setData({labelAlias:e.label||"label",valueAlias:e.value||"value"})},value(e){const{options:t,labelAlias:a,valueAlias:s}=this.data;if(this.data.multiple&&!Array.isArray(e))throw TypeError("应传入数组类型的 value");const o=t.find((t=>t[s]===e));o&&this.setData({computedLabel:o[a]})},"label, computedLabel, disabled"(){var e;null===(e=this.$parent)||void 0===e||e.getAllItems()},show(e){e&&this.getParentBottom((()=>{this.setData({wrapperVisible:!0})}))}},this.methods={closeDropdown(){var e;null===(e=this.$parent)||void 0===e||e.setData({activeIdx:-1}),this.setData({show:!1}),this.triggerEvent("close")},getParentBottom(e){getRect(this.$parent,`#${prefix}-bar`).then((t=>{this.setData({top:t.bottom,maskHeight:t.top},e)}))},handleTreeClick(e){const{level:t,value:a}=e.currentTarget.dataset,{value:s}=this.data;s[t]=a,this._trigger("change",{value:s})},handleRadioChange(e){const{value:t}=e.detail;if(this._trigger("change",{value:t}),this.data.multiple){const e=this.data.options.find((e=>t.includes(e.value)));e&&(this.data.firstCheckedValue=e.value)}else this.closeDropdown()},handleMaskClick(){var e;(null===(e=this.$parent)||void 0===e?void 0:e.properties.closeOnClickOverlay)&&this.closeDropdown()},handleReset(){this._trigger("change",{value:[]}),this._trigger("reset")},handleConfirm(){this._trigger("confirm",{value:this.data.value}),this.closeDropdown(),this.setData({firstCheckedValue:this.data.firstCheckedValue})},onLeaved(){this.setData({wrapperVisible:!1})}}}};DropdownMenuItem=__decorate([wxComponent()],DropdownMenuItem);export default DropdownMenuItem;