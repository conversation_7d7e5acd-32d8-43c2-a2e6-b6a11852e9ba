/* pages/maintenance/checkin-step2.wxss */
.checkin-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 步骤条样式（复用step1的样式） */
.steps-container {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  background: #e0e0e0;
  color: #999;
  transition: all 0.3s ease;
}

.step.active .step-icon {
  background: #1976d2;
  color: #fff;
  transform: scale(1.1);
}

.step.completed .step-icon {
  background: #4caf50;
  color: #fff;
}

.step-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.step.active .step-title {
  color: #1976d2;
  font-weight: 600;
}

.step.completed .step-title {
  color: #4caf50;
  font-weight: 600;
}

.step-line {
  position: absolute;
  top: 32rpx;
  left: 50%;
  width: 100%;
  height: 2rpx;
  background: #e0e0e0;
  z-index: -1;
}

.step:last-child .step-line {
  display: none;
}

.step-line.completed {
  background: #4caf50;
}

/* 内容容器 */
.content-container {
  padding: 32rpx;
}

/* 提示信息 */
.tip-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tip-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  display: block;
}

.tip-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 人脸识别区域 */
.face-recognition-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

/* 相机容器 */
.camera-container {
  position: relative;
  width: 100%;
  height: 600rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #000;
}

.camera-preview {
  width: 100%;
  height: 100%;
}

/* 人脸框指引 */
.face-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.face-frame {
  width: 300rpx;
  height: 400rpx;
  position: relative;
  margin-bottom: 32rpx;
}

.frame-corner {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #fff;
}

.frame-corner.tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.frame-corner.tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.frame-corner.bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.frame-corner.br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.guide-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 拍照控制 */
.capture-controls {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
}

.capture-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 6rpx solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.capture-btn:active {
  transform: scale(0.95);
}

.capture-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #1976d2;
}

/* 照片预览 */
.photo-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.preview-image {
  width: 400rpx;
  height: 500rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
}

.preview-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.retry-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #1976d2;
  color: #fff;
}

/* 识别结果 */
.recognition-result {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.result-header {
  text-align: center;
}

.result-status {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.result-status.success {
  background: #e8f5e8;
  color: #4caf50;
}

.result-status.failed {
  background: #ffebee;
  color: #f44336;
}

.status-icon {
  font-size: 24rpx;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.user-id,
.confidence {
  font-size: 24rpx;
  color: #666;
}

.verification-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
  border-left: 4rpx solid #1976d2;
}

.verify-time,
.verify-location {
  font-size: 24rpx;
  color: #1976d2;
}

.error-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 64rpx;
}

.error-message {
  font-size: 28rpx;
  color: #f44336;
  font-weight: 500;
}

.error-suggestion {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.retry-recognition-btn {
  width: 200rpx;
  height: 64rpx;
  background: #1976d2;
  color: #fff;
  border-radius: 32rpx;
  font-size: 28rpx;
  border: none;
  margin-top: 16rpx;
}

/* 识别中状态 */
.recognizing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.recognizing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  color: #fff;
}

.recognizing-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.recognizing-text {
  font-size: 32rpx;
  font-weight: 500;
}

.recognizing-tip {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 安全提示 */
.safety-tips {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.tips-icon {
  font-size: 32rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
}

.back-btn,
.next-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.back-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.back-btn:active {
  background: #e0e0e0;
}

.next-btn {
  flex: 2;
}

.next-btn:not(.disabled) {
  background: #1976d2;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.next-btn:not(.disabled):active {
  background: #1565c0;
  transform: translateY(2rpx);
}

.next-btn.disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.btn-arrow {
  font-size: 28rpx;
}
