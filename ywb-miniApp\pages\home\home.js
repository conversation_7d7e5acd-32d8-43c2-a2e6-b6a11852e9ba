// pages/home/<USER>
const app = getApp()
const { projectAPI, maintenanceAPI } = require('../../utils/api')
const { formatTime, getCurrentTime } = require('../../utils/util')

Page({
  data: {
    userInfo: {},
    projectList: [],
    loading: true,
    refreshing: false,
    todayStats: null,
    todayDate: '',
    noticeList: []
  },

  onLoad() {
    console.log('运维首页加载')
    this.initPage()
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.token) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
    
    // 刷新数据
    this.loadPageData()
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  // 初始化页面
  initPage() {
    // 设置用户信息
    this.setData({
      userInfo: app.globalData.userInfo || {},
      todayDate: formatTime(new Date(), 'MM-DD')
    })
    
    // 加载页面数据
    this.loadPageData()
  },

  // 加载页面数据
  loadPageData() {
    this.setData({ loading: true })
    
    Promise.all([
      this.loadProjectList(),
      this.loadTodayStats(),
      this.loadNoticeList()
    ]).finally(() => {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    })
  },

  // 加载项目列表
  loadProjectList() {
    return projectAPI.getProjectList({
      userId: app.globalData.userInfo.id,
      status: 1 // 只加载启用的项目
    }).then(res => {
      const projectList = res.data.list || []
      
      // 为每个项目加载统计数据
      const promises = projectList.map(project => {
        return this.loadProjectStats(project.id).then(stats => {
          project.myBriefingCount = stats.briefingCount || 0
          project.myMaintenanceCount = stats.maintenanceCount || 0
          return project
        }).catch(() => {
          project.myBriefingCount = 0
          project.myMaintenanceCount = 0
          return project
        })
      })
      
      return Promise.all(promises).then(updatedProjects => {
        this.setData({ projectList: updatedProjects })
      })
    }).catch(error => {
      console.error('加载项目列表失败:', error)
      wx.showToast({
        title: '加载项目失败',
        icon: 'none'
      })
    })
  },

  // 加载项目统计数据
  loadProjectStats(projectId) {
    return projectAPI.getUserProjectStats(app.globalData.userInfo.id, projectId)
      .then(res => res.data)
      .catch(() => ({ briefingCount: 0, maintenanceCount: 0 }))
  },

  // 加载今日统计
  loadTodayStats() {
    const today = formatTime(new Date(), 'YYYY-MM-DD')
    
    return maintenanceAPI.getUserMaintenanceStats({
      userId: app.globalData.userInfo.id,
      startDate: today,
      endDate: today
    }).then(res => {
      this.setData({
        todayStats: {
          briefingCount: res.data.briefingCount || 0,
          checkinCount: res.data.checkinCount || 0,
          siteCount: res.data.siteCount || 0,
          workDuration: res.data.workDuration || 0
        }
      })
    }).catch(error => {
      console.error('加载今日统计失败:', error)
    })
  },

  // 加载通知公告
  loadNoticeList() {
    // TODO: 实现通知公告API调用
    // 暂时使用模拟数据
    const mockNotices = [
      {
        id: 1,
        content: '系统将于今晚22:00-24:00进行维护升级',
        createTime: '10:30'
      },
      {
        id: 2,
        content: '新增AI人脸识别功能，提升安全验证效率',
        createTime: '09:15'
      }
    ]
    
    this.setData({ noticeList: mockNotices })
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.loadPageData().finally(() => {
      this.setData({ refreshing: false })
    })
  },

  // 查看项目详情
  viewProjectDetail(e) {
    const project = e.currentTarget.dataset.project
    
    wx.navigateTo({
      url: `/pages/project/project-detail?id=${project.id}`
    })
  },

  // 新增运维
  newMaintenance(e) {
    const project = e.currentTarget.dataset.project
    
    wx.navigateTo({
      url: `/pages/maintenance/new-maintenance?projectId=${project.id}&projectName=${encodeURIComponent(project.projectName)}`
    })
  },

  // 快速安全交底
  quickSafetyBriefing() {
    const { projectList } = this.data
    
    if (projectList.length === 0) {
      wx.showToast({
        title: '暂无可用项目',
        icon: 'none'
      })
      return
    }
    
    if (projectList.length === 1) {
      // 只有一个项目，直接跳转
      wx.navigateTo({
        url: `/pages/maintenance/safety-briefing?projectId=${projectList[0].id}`
      })
    } else {
      // 多个项目，显示选择列表
      const projectNames = projectList.map(p => p.projectName)
      
      wx.showActionSheet({
        itemList: projectNames,
        success: (res) => {
          const selectedProject = projectList[res.tapIndex]
          wx.navigateTo({
            url: `/pages/maintenance/safety-briefing?projectId=${selectedProject.id}`
          })
        }
      })
    }
  },

  // 快速打卡
  quickCheckin() {
    const { projectList } = this.data
    
    if (projectList.length === 0) {
      wx.showToast({
        title: '暂无可用项目',
        icon: 'none'
      })
      return
    }
    
    // 检查今日是否已交底
    this.checkTodayBriefing().then(hasBriefing => {
      if (!hasBriefing) {
        wx.showModal({
          title: '提示',
          content: '今日尚未进行安全交底，请先完成安全交底后再进行打卡',
          confirmText: '去交底',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.quickSafetyBriefing()
            }
          }
        })
        return
      }
      
      // 已交底，可以打卡
      if (projectList.length === 1) {
        wx.navigateTo({
          url: `/pages/maintenance/checkin-step1?projectId=${projectList[0].id}`
        })
      } else {
        const projectNames = projectList.map(p => p.projectName)
        
        wx.showActionSheet({
          itemList: projectNames,
          success: (res) => {
            const selectedProject = projectList[res.tapIndex]
            wx.navigateTo({
              url: `/pages/maintenance/checkin-step1?projectId=${selectedProject.id}`
            })
          }
        })
      }
    })
  },

  // 快速站点管理
  quickSiteManage() {
    const { projectList } = this.data
    
    if (projectList.length === 0) {
      wx.showToast({
        title: '暂无可用项目',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/site/site-selection'
    })
  },

  // 检查今日是否已交底
  checkTodayBriefing() {
    const { projectList } = this.data
    const userId = app.globalData.userInfo.id
    
    // 检查所有项目是否都已交底
    const promises = projectList.map(project => {
      return safetyAPI.checkTodayBriefing(userId, project.id)
        .then(res => res.data.hasBriefing)
        .catch(() => false)
    })
    
    return Promise.all(promises).then(results => {
      // 至少有一个项目已交底就返回true
      return results.some(result => result)
    })
  },

  // 查看通知详情
  viewNotice(e) {
    const notice = e.currentTarget.dataset.notice
    
    wx.showModal({
      title: '通知详情',
      content: notice.content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '运维宝 - 智能运维管理平台',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-cover.png'
    }
  },

  onShareTimeline() {
    return {
      title: '运维宝 - 智能运维管理平台',
      imageUrl: '/images/share-cover.png'
    }
  }
})
